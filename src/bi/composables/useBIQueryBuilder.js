import { supportedAggregations } from '@sensehawk/query-generator/aggregations';
import IconHawkArrowDown from '~icons/hawk/arrow-down';
import IconHawkArrowUp from '~icons/hawk/arrow-up';
import IconHawkBinary from '~icons/hawk/binary';
import IconHawkBoolean from '~icons/hawk/boolean';
import IconHawkBracketsEllipses from '~icons/hawk/brackets-ellipses';
import calendar from '~icons/hawk/calendar';
import IconHawkClock from '~icons/hawk/clock';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkInnerJoin from '~icons/hawk/inner-join';
import IconHawkOuterJoin from '~icons/hawk/outer-join';
import IconHawkRightJoin from '~icons/hawk/right-join';
import IconHawkTypeOne from '~icons/hawk/type-one';
import IconHawkLeftJoin from '~icons/hawk/vector-join';

function getIconsForType(type) {
  const icons_type_map = {
    'string': IconHawkTypeOne,
    'text': IconHawkTypeOne,
    'varchar': IconHawkTypeOne,
    'char': IconHawkTypeOne,
    'numeric': IconHawkHashTwo,
    'integer': IconHawkHashTwo,
    'bigint': IconHawkHashTwo,
    'smallint': IconHawkHashTwo,
    'decimal': IconHawkHashTwo,
    'float': IconHawkHashTwo,
    'double': IconHawkHashTwo,
    'real': IconHawkHashTwo,
    'number': IconHawkHashTwo,
    'double precision': IconHawkHashTwo,
    'date': calendar,
    'datetime': calendar,
    'timestamp': calendar,
    'timestamptz': IconHawkClock,
    'boolean': IconHawkBoolean,
    'bool': IconHawkBoolean,
    'interval': IconHawkClock,
    'time': IconHawkClock,
    'binary': IconHawkBinary,
    'bytea': IconHawkBinary,
    'json': IconHawkBracketsEllipses,
    'jsonb': IconHawkBracketsEllipses,
    'uuid': IconHawkHashTwo,
    'id': IconHawkHashTwo,
    'function': IconHawkFunction,
    'formula': IconHawkFormula,
    'asc': IconHawkArrowUp,
    'desc': IconHawkArrowDown,
    'joins': {
      inner: IconHawkInnerJoin,
      outer: IconHawkOuterJoin,
      right: IconHawkRightJoin,
      left: IconHawkLeftJoin,
    },
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

const aggregations = supportedAggregations();
const supported_types = Array.from(new Set(aggregations.map(agg => agg.supportedTypes).flat()));

const operators_for_type = Array.from(supported_types).reduce((obj, type) => {
  obj[type] = aggregations.map(agg => ({
    label: agg.label,
    name: agg.name,
    output_type: agg.outputTypes[type],
    type: agg.type,
  })).filter(agg => agg.output_type);
  return obj;
}, {});

const aggregations_map = {};
aggregations.forEach((agg) => {
  aggregations_map[agg.name] = agg;
});

operators_for_type.array = [{
  label: 'Extract items',
  name: 'unnest',
  output_type: 'text',
}];
function getOperatorsForType(type) {
  return operators_for_type[type] || [];
}

function buildHierarchy(cols) {
  const root = [];

  const findHierarchyNode = (arr, label) => arr.find(n => n.label === label && n.is_hierarchy);

  cols.forEach((col) => {
    if (Array.isArray(col.path) && col.path.length > 0) {
      let currentLevel = root;
      // Walk/create nodes for each part in the path.
      col.path.forEach((part, idx) => {
        const isLast = idx === col.path.length - 1;
        // If it's the last part, ensure there's a node and push the column into its children.
        if (isLast) {
          let node = findHierarchyNode(currentLevel, part);
          if (!node) {
            node = { label: part, is_hierarchy: true, children: [] };
            currentLevel.push(node);
          }
          node.children.push(col);
        }
        else {
          // intermediate node: ensure it exists and descend into its children
          let node = findHierarchyNode(currentLevel, part);
          if (!node) {
            node = { label: part, is_hierarchy: true, children: [] };
            currentLevel.push(node);
          }
          currentLevel = node.children;
        }
      });
    }
    else {
      // No path => top-level column
      root.push(col);
    }
  });

  return root;
}

export function useBIQueryBuilder() {
  const getColumnText = column_name => `${column_name}`;
  const getAggregationText = agg => `${agg} of `;
  const getBinText = bin => ` (${bin})`;
  const getTableText = table_name => `${table_name} ⇒ `;
  const constructFieldName = (field, selected_table_name) => field.table_name ? (`${field.table_name}→${field.label}`) : (`${selected_table_name}→${field.label}`);
  const constructAlias = (fn, param) => param ? fn(param) : ''; // this will construct the alias for the column ${table_name} -- ${agg} ${column_name}
  const constructColumnAlias = ({ table_name, column_name, agg, bin }) => constructAlias(getTableText, table_name) + constructAlias(getAggregationText, agg) + constructAlias(getColumnText, column_name) + constructAlias(getBinText, bin);

  function createDefaultSelection() {
    return {
      columns: [],
      orderBy: [],
      limit: null,
      filters: [],
      joins: [],
    };
  }

  function getStageConfig(stages) {
    if (stages[0]?.columns?.length === 0)
      return;

    const getTableConfig = stage => ({
      table: stage.table,
      orderBy: stage.orderBy,
      limit: stage.limit ? stage.limit : undefined,
      filters: stage.filters,
      columns: stage.columns,
      joins: stage.joins,
    });
    const config = stages.filter(stage => stage?.columns?.length > 0).map(stage => getTableConfig(stage));
    return config;
  }

  return {
    constructColumnAlias,
    constructFieldName,
    getIconsForType,
    getOperatorsForType,
    createDefaultSelection,
    buildHierarchy,
    getStageConfig,
    aggregations_map,
  };
}
