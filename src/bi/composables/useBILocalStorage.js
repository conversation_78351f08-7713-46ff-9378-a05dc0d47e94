const BI_STORAGE_KEYS = {
  DASHBOARDS: 'bi_dashboards',
  WIDGETS: 'bi_widgets',
};

const DEFAULT_WIDGETS_DATA = {
  '0805de09-b5c8-42ff-9cde-9ce1368e8f29': {
    config: {
      query: {},
      chart: {
        type: 'rich_text',
        content: '<p style="text-align: center"><span style="color: rgb(255, 255, 255)"><strong>CONSTRUCTION</strong></span></p>',
        is_transparent_background: false,
        background_color: '#598488',
      },
    },
  },
  'c30d2216-e4c3-40f6-95ca-a3c40093e028': {
    config: {
      query: {},
      chart: {
        type: 'rich_text',
        content: '<p style="text-align: center"><span style="color: rgb(255, 255, 255)"><strong>CONSTRUCTION</strong></span></p>',
        is_transparent_background: false,
        background_color: '#4778b8',
      },
    },
  },
  '4ccbb330-fc84-4a26-836b-e9e6fe673994': {
    config: {
      query: {},
      chart: {
        type: 'rich_text',
        content: '<p style="text-align: center"><span style="color: rgb(255, 255, 255)"><strong>OVERALL</strong></span></p>',
        is_transparent_background: false,
        background_color: '#586f89',
      },
    },
    name: 'undefined - Copy',
  },
  'af6c14a5-3182-4596-8bff-56b1c3ba0c9e': {
    config: {
      query: {},
      chart: {
        type: 'rich_text',
        content: '<p style="text-align: center"><span style="color: rgb(255, 255, 255)"><strong>HSE</strong></span></p>',
        is_transparent_background: false,
        background_color: '#e28743',
      },
    },
    name: 'undefined - Copy',
  },
  '42cfd15b-433e-4f30-8aa3-a45f8898f73f': {
    config: {
      query: {},
      chart: {
        type: 'rich_text',
        content: '<p style="text-align: center"><span style="color: rgb(255, 255, 255)"><strong>NCR &amp; HSE</strong></span></p>',
        is_transparent_background: false,
        background_color: '#757595',
      },
    },
  },
  'fa57e24a-1707-40c0-b61d-c94ca30ff9e2': {
    config: {
      query: {},
      chart: {
        type: 'rich_text',
        content: '<p style="text-align: center"><span style="color: #FFFFFF"><strong>Executive report</strong></span></p>',
        is_transparent_background: false,
        background_color: '#9481d5',
      },
    },
  },
  '804a24d2-1ae3-42c6-929a-40e1a95b8c6f': {
    name: 'Tracker progress',
    config: {
      chart: {
        type: 'pivot_table',
        pivot_rows: [
          'Layer',
          'Sublayer',
        ],
        pivot_columns: [
          'Activity',
          'Subactivity',
        ],
        pivot_values: [
          {
            column: 'Sum of Completed',
            aggregation: 'sum',
          },
          {
            column: 'Sum of Scope',
            aggregation: 'sum',
          },
          {
            column: 'Sum of Remaining',
            aggregation: 'sum',
          },
        ],
        show_row_totals: true,
        show_column_totals: true,
        show_grand_total: false,
        conditional_formatting: [
          {
            uid: 'fb76407c-98ee-4b1b-bf3e-04f6b0a5b3f7',
            field: 'Sum of Remaining',
            formatting_style: 'color_range',
            operator: 'equal',
            apply_to: 'column',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#F2726F',
            color_range: 16,
          },
          {
            uid: '313b047e-c4f2-433f-8f9f-41bbc9506d68',
            field: 'Sum of Scope',
            formatting_style: 'color_range',
            operator: 'equal',
            apply_to: 'column',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#4778b8',
            color_range: 16,
          },
          {
            uid: '1bdeaba6-e7f5-404e-bf36-c7ef4e0bf2f1',
            field: 'Sum of Completed',
            formatting_style: 'single_color',
            operator: 'equal',
            value: '0',
            apply_to: 'column',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#d3745f',
            color_range: 5,
          },
        ],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_data',
            orderBy: [],
            filters: [
              {
                field: 'progress_data→Activity',
                op: 'in',
                value: [
                  '[Cor] 2.0 Tracker',
                ],
              },
            ],
            columns: [
              {
                field: 'progress_data→Activity',
                alias: 'Activity',
              },
              {
                field: 'progress_data→Subactivity',
                alias: 'Subactivity',
              },
              {
                field: 'progress_data→Layer',
                alias: 'Layer',
              },
              {
                field: 'progress_data→Sublayer',
                alias: 'Sublayer',
              },
              {
                field: 'progress_data→Completed',
                agg: 'sum',
                alias: 'Sum of Completed',
              },
              {
                field: 'progress_data→Scope',
                agg: 'sum',
                alias: 'Sum of Scope',
              },
              {
                field: 'progress_data→Remaining',
                agg: 'sum',
                alias: 'Sum of Remaining',
              },
            ],
          },
        ],
      },
    },
  },
  'fc6cf328-a945-4c96-9ffe-35906b9a4c67': {
    name: 'Activity wise progress',
    config: {
      chart: {
        type: 'horizontalBar',
        data: {
          category: 'Subactivity',
          values: [
            'Sum of Work done',
            'Maximum of Scope',
          ],
          stackBy: 'Sublayer',
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        series: {
          'Sum of Work done': {
            name: 'Sum of Work done',
            type: 'bar',
            color: 'distinct',
            stack: 'group1',
            lineColor: 'distinct',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Maximum of Scope': {
            name: 'Maximum of Scope',
            type: 'bar',
            color: '#c17e57',
            stack: 'group1',
            lineColor: '#c17e57',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'top',
        },
        dataValues: {
          show: true,
          compact: true,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_history',
            orderBy: [],
            filters: [
              {
                field: 'progress_history→Activity',
                op: 'in',
                value: [
                  '[Cor] 2.1 Mechanical-Piles',
                ],
              },
            ],
            columns: [
              {
                field: 'progress_history→Subactivity',
                alias: 'Subactivity',
              },
              {
                field: 'progress_history→Sublayer',
                alias: 'Sublayer',
              },
              {
                field: 'progress_history→Work done',
                agg: 'sum',
                alias: 'Sum of Work done',
              },
              {
                field: 'progress_history→Scope',
                agg: 'max',
                alias: 'Maximum of Scope',
              },
              {
                expr: 'round([Sum of Work done]*100/[Maximum of Scope], 2)',
                type: 'column',
                alias: '% done',
              },
            ],
          },
        ],
      },
    },
  },
  '70248e64-e52c-4dc7-8868-a9536dbc9406': {
    name: 'Documents by category',
    config: {
      chart: {
        type: 'horizontalBar',
        data: {
          category: 'category',
          values: [
            'Count of status',
          ],
          stackBy: 'status',
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        series: {
          'Count of status': {
            name: 'Count of status',
            type: 'bar',
            color: 'distinct',
            stack: 'group1',
            lineColor: 'distinct',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'documents',
            orderBy: [],
            filters: [
              {
                field: 'documents→status',
                op: 'is_not_empty',
              },
            ],
            columns: [
              {
                field: 'documents→category',
                alias: 'category',
              },
              {
                field: 'documents→status',
                alias: 'status',
              },
              {
                field: 'documents→status',
                agg: 'count',
                alias: 'Count of status',
              },
            ],
          },
        ],
      },
    },
  },
  '918b25ab-14d1-482a-bb55-98ebf4e33ce1': {
    name: 'Status breakdown',
    config: {
      chart: {
        type: 'donut',
        data: {
          category: 'status',
          values: [
            'Count of status',
          ],
        },
        interactions: {},
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'documents',
            orderBy: [],
            filters: [
              {
                field: 'documents→status',
                op: 'is_not_empty',
              },
            ],
            columns: [
              {
                field: 'documents→status',
                alias: 'status',
              },
              {
                field: 'documents→status',
                agg: 'count',
                alias: 'Count of status',
              },
            ],
          },
        ],
      },
    },
  },
  '92e284d3-2677-469e-9d16-9da9abce1fae': {
    name: 'As built documents',
    config: {
      chart: {
        type: 'number_chart',
        columns_map: {
          'Count of id': {
            key: 'Count of id',
            width: null,
            visible: true,
            order_index: 0,
          },
        },
        layout_category: 'AS-BUILT count',
        layout_values: [
          {
            value: 'IFA count',
          },
        ],
        show_percentage_change: true,
        switch_positive_negative_colors: false,
        compact: false,
        prefix: '',
        suffix: null,
        number_widget_color: '#4778b8',
        stack_by: 'none',
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'documents',
            orderBy: [],
            filters: [],
            columns: [
              {
                expr: 'if([documents→status] = "AS-BUILT", 1, 0)',
                type: 'column',
                skip: true,
                alias: 'As built',
              },
              {
                expr: 'if([documents→status] = "IFA", 1, 0)',
                type: 'column',
                skip: true,
                alias: 'IFA',
              },
              {
                expr: 'Sum([As built])',
                type: 'aggregation',
                alias: 'AS-BUILT count',
              },
              {
                expr: 'Sum([IFA])',
                type: 'aggregation',
                alias: 'IFA count',
              },
            ],
          },
        ],
      },
    },
  },
  '4f44a067-f77d-49bb-9b63-df59417d0870': {
    name: 'Subactivity breakdown',
    config: {
      chart: {
        type: 'pie',
        data: {
          category: 'Subactivity',
          values: [
            'Sum of Completed',
          ],
        },
        interactions: {},
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_data',
            orderBy: [],
            filters: [
              {
                field: 'progress_data→Activity',
                op: 'in',
                value: [
                  '[Cor] 2.1 Mechanical-Piles',
                ],
              },
            ],
            columns: [
              {
                field: 'progress_data→Subactivity',
                alias: 'Subactivity',
              },
              {
                field: 'progress_data→Completed',
                agg: 'sum',
                alias: 'Sum of Completed',
              },
            ],
          },
        ],
      },
    },
  },
  '474be9be-e7dc-4631-a5cc-77c14c06a012': {
    name: 'Month wise work done',
    config: {
      chart: {
        type: 'pyramid',
        data: {
          category: 'Date (Month)',
          values: [
            'Sum of Work done',
          ],
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
        chartSpecific: {
          pyramid: {
            labelsAtCenter: false,
            showPercentages: true,
            is3D: true,
          },
        },
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_history',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'progress_history→Date',
                dateBin: 'month',
                alias: 'Date (Month)',
              },
              {
                field: 'progress_history→Work done',
                agg: 'sum',
                alias: 'Sum of Work done',
              },
            ],
          },
        ],
      },
    },
  },
  '05bf089a-2420-4e6b-ad5d-8f9ed0d1fe94': {
    name: 'Week wise progress',
    config: {
      chart: {
        type: 'mixed',
        data: {
          category: 'Date (Week)',
          values: [
            'Sum of Work done',
            'Cumulative Sum of Work done',
          ],
          stackBy: null,
        },
        interactions: {
          dataZoom: {
            enabled: false,
            type: 'both',
          },
        },
        series: {
          'Sum of Work done': {
            name: 'Sum of Work done',
            type: 'bar',
            yAxisIndex: 0,
            color: '#4778b8',
            stack: false,
            lineColor: '#4778b8',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cumulative Sum of Work done': {
            name: 'Cumulative Sum of Work done',
            type: 'line',
            yAxisIndex: 1,
            color: '#F2726F',
            stack: false,
            lineColor: '#F2726F',
            lineWidth: 3,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: true,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          secondaryValueLabels: 'show',
          valueScale: 'linear',
          secondaryValueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_history',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'progress_history→Date',
                dateBin: 'week',
                alias: 'Date (Week)',
              },
              {
                field: 'progress_history→Work done',
                agg: 'sum',
                alias: 'Sum of Work done',
              },
              {
                field: 'progress_history→Work done',
                agg: 'cumsum',
                alias: 'Cumulative Sum of Work done',
              },
            ],
          },
        ],
      },
    },
  },
  'd5e063f4-ec2e-443b-bd94-6bc91911adb4': {
    name: 'S-Curve',
    config: {
      chart: {
        type: 'mixed',
        data: {
          category: 'date (Week)',
          values: [
            'Planned',
            'Actual',
            'Cum. planned',
            'Cum. Actual',
          ],
          stackBy: null,
        },
        interactions: {
          dataZoom: {
            enabled: false,
            type: 'both',
          },
        },
        series: {
          'Planned': {
            name: 'Planned',
            type: 'bar',
            yAxisIndex: 0,
            color: '#586f89',
            stack: false,
            lineColor: '#586f89',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Actual': {
            name: 'Actual',
            type: 'bar',
            yAxisIndex: 0,
            color: '#82b2d4',
            stack: false,
            lineColor: '#82b2d4',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cum. planned': {
            name: 'Cum. planned',
            type: 'line',
            yAxisIndex: 1,
            color: '#598488',
            stack: false,
            lineColor: '#598488',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cum. Actual': {
            name: 'Cum. Actual',
            type: 'line',
            yAxisIndex: 1,
            color: '#e9c45d',
            stack: false,
            lineColor: '#e9c45d',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: false,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          secondaryValueLabels: 'show',
          valueScale: 'linear',
          secondaryValueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: '2025_dates',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: '2025_dates→date',
                dateBin: 'week',
                alias: 'date (Week)',
              },
              {
                expr: 'Sum([activities→weight] * [progress_timelines→planned_progress])',
                type: 'aggregation',
                alias: 'Planned',
              },
              {
                expr: 'Sum([activities→weight] * [progress_timelines→actual_progress])',
                type: 'aggregation',
                alias: 'Actual',
              },
              {
                expr: 'CumulativeSum([Planned])',
                type: 'aggregation',
                alias: 'Cum. planned',
              },
              {
                expr: 'CumulativeSum([Actual])',
                type: 'aggregation',
                alias: 'cumulative actual',
              },
            ],
            joins: [
              {
                table: {
                  name: 'progress_timelines',
                },
                label: 'progress_timelines',
                type: 'left',
                on: [
                  {
                    left: '2025_dates→date',
                    left_label: 'date',
                    left_table: '2025_dates',
                    op: '=',
                    right: 'progress_timelines→date',
                    right_label: 'date',
                    right_table: 'progress_timelines',
                  },
                ],
              },
              {
                table: {
                  name: 'activities',
                },
                label: 'activities',
                type: 'left',
                on: [
                  {
                    left: 'progress_timelines→id',
                    left_label: 'id',
                    left_table: 'progress_timelines',
                    op: '=',
                    right: 'activities→id',
                    right_label: 'id',
                    right_table: 'activities',
                  },
                ],
              },
            ],
          },
          {
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'results→cumulative actual',
                skip: true,
                alias: 'cumulative actual',
              },
              {
                expr: 'nullIf(if(\n  [date (Week)] > date(now()),\n  -1,\n  [cumulative actual]\n), -1)',
                type: 'column',
                alias: 'Cum. Actual',
              },
            ],
          },
        ],
      },
    },
  },
  '8999ee1e-1f5c-4b7c-a003-c64894ca03e7': {
    config: {
      chart: {
        type: 'table',
        columns_map: {
          'status': {
            key: 'status',
            width: null,
            visible: true,
            order_index: 0,
          },
          'PENDING': {
            key: 'PENDING',
            width: null,
            visible: true,
            order_index: 1,
          },
          'ON TRACK': {
            key: 'ON TRACK',
            width: null,
            visible: true,
            order_index: 2,
          },
          'DELAYED': {
            key: 'DELAYED',
            width: null,
            visible: true,
            order_index: 3,
          },
          'CUMULATIVE PENDING': {
            key: 'CUMULATIVE PENDING',
            width: null,
            visible: true,
            order_index: 4,
          },
          'CUMULATIVE ON TRACK': {
            key: 'CUMULATIVE ON TRACK',
            width: null,
            visible: true,
            order_index: 5,
          },
          'CUMULATIVE DELAYED': {
            key: 'CUMULATIVE DELAYED',
            width: null,
            visible: true,
            order_index: 6,
          },
        },
        show_row_headers: false,
        conditional_formatting: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'submission_events_ncr',
            orderBy: [],
            filters: [
              {
                field: 'submission_events_ncr→status',
                op: 'in',
                value: [
                  'Execution',
                  'Issuance',
                ],
              },
            ],
            columns: [
              {
                field: 'submission_events_ncr→status',
                alias: 'status',
              },
              {
                field: 'submission_events_ncr→due_date',
                dateBin: 'month',
                skip: false,
                alias: 'due_date (Month)',
              },
              {
                expr: 'if(\n  isNull([submission_events_ncr→submitted_at]),\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'pending',
              },
              {
                expr: 'if(\n  [submission_events_ncr→submitted_at] <= [submission_events_ncr→due_date],\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'on/before',
              },
              {
                expr: 'if(\n  [submission_events_ncr→submitted_at] > [submission_events_ncr→due_date],\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'after',
              },
              {
                expr: 'Sum([pending])',
                type: 'aggregation',
                alias: 'Pending',
              },
              {
                expr: 'Sum([on/before])',
                type: 'aggregation',
                alias: 'On/Before',
              },
              {
                expr: 'Sum([after])',
                type: 'aggregation',
                alias: 'After',
              },
              {
                expr: 'CumulativeSum([pending])',
                type: 'aggregation',
                alias: 'Cumulative pending',
              },
              {
                expr: 'CumulativeSum([on/before])',
                type: 'aggregation',
                alias: 'Cumulative On/Before',
              },
              {
                expr: 'CumulativeSum([after])',
                type: 'aggregation',
                alias: 'Cumulative After',
              },
            ],
          },
          {
            orderBy: [
              {
                column: 'status',
                direction: 'desc',
              },
            ],
            filters: [],
            columns: [
              {
                field: 'results→status',
                alias: 'status',
              },
              {
                field: 'results→due_date (Month)',
                skip: true,
                alias: 'due_date (Month)',
              },
              {
                field: 'results→Pending',
                skip: true,
                alias: 'Pending',
              },
              {
                field: 'results→On/Before',
                skip: true,
                alias: 'On/Before',
              },
              {
                field: 'results→After',
                skip: true,
                alias: 'After',
              },
              {
                field: 'results→Cumulative pending',
                skip: true,
                alias: 'Cumulative pending',
              },
              {
                field: 'results→Cumulative On/Before',
                skip: true,
                alias: 'Cumulative On/Before',
              },
              {
                field: 'results→Cumulative After',
                skip: true,
                alias: 'Cumulative After',
              },
              {
                field: 'results→Pending',
                agg: 'sum',
                skip: false,
                alias: 'PENDING',
              },
              {
                field: 'results→On/Before',
                agg: 'sum',
                skip: false,
                alias: 'ON TRACK',
              },
              {
                field: 'results→After',
                agg: 'sum',
                skip: false,
                alias: 'DELAYED',
              },
              {
                field: 'results→Cumulative pending',
                agg: 'max',
                alias: 'CUMULATIVE PENDING',
              },
              {
                field: 'results→Cumulative On/Before',
                agg: 'max',
                alias: 'CUMULATIVE ON TRACK',
              },
              {
                field: 'results→Cumulative After',
                agg: 'max',
                alias: 'CUMULATIVE DELAYED',
              },
            ],
          },
        ],
      },
    },
    name: 'NCR metrics',
  },
  '66f0e03f-e916-4640-83bf-f12ccea5f380': {
    config: {
      chart: {
        type: 'table',
        columns_map: {
          'status': {
            key: 'status',
            width: null,
            visible: true,
            order_index: 0,
          },
          'PENDING': {
            key: 'PENDING',
            width: null,
            visible: true,
            order_index: 1,
          },
          'ON TRACK': {
            key: 'ON TRACK',
            width: null,
            visible: true,
            order_index: 2,
          },
          'DELAYED': {
            key: 'DELAYED',
            width: null,
            visible: true,
            order_index: 3,
          },
          'CUMULATIVE PENDING': {
            key: 'CUMULATIVE PENDING',
            width: null,
            visible: true,
            order_index: 4,
          },
          'CUMULATIVE ON TRACK': {
            key: 'CUMULATIVE ON TRACK',
            width: null,
            visible: true,
            order_index: 5,
          },
          'CUMULATIVE DELAYED': {
            key: 'CUMULATIVE DELAYED',
            width: null,
            visible: true,
            order_index: 6,
          },
        },
        show_row_headers: false,
        conditional_formatting: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'submission_events_ncr',
            orderBy: [],
            filters: [
              {
                field: 'submission_events_ncr→status',
                op: 'in',
                value: [
                  'Execution',
                  'Issuance',
                ],
              },
              {
                field: 'due_date (Month)',
                op: 'between',
                value: [
                  '2025-03-01T10:15:00.000Z',
                  '2025-03-31T10:15:00.000Z',
                ],
              },
            ],
            columns: [
              {
                field: 'submission_events_ncr→status',
                alias: 'status',
              },
              {
                field: 'submission_events_ncr→due_date',
                dateBin: 'month',
                skip: false,
                alias: 'due_date (Month)',
              },
              {
                expr: 'if(\n  isNull([submission_events_ncr→submitted_at]),\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'pending',
              },
              {
                expr: 'if(\n  [submission_events_ncr→submitted_at] <= [submission_events_ncr→due_date],\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'on/before',
              },
              {
                expr: 'if(\n  [submission_events_ncr→submitted_at] > [submission_events_ncr→due_date],\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'after',
              },
              {
                expr: 'Sum([pending])',
                type: 'aggregation',
                alias: 'Pending',
              },
              {
                expr: 'Sum([on/before])',
                type: 'aggregation',
                alias: 'On/Before',
              },
              {
                expr: 'Sum([after])',
                type: 'aggregation',
                alias: 'After',
              },
              {
                expr: 'CumulativeSum([pending])',
                type: 'aggregation',
                alias: 'Cumulative pending',
              },
              {
                expr: 'CumulativeSum([on/before])',
                type: 'aggregation',
                alias: 'Cumulative On/Before',
              },
              {
                expr: 'CumulativeSum([after])',
                type: 'aggregation',
                alias: 'Cumulative After',
              },
            ],
          },
          {
            orderBy: [
              {
                column: 'status',
                direction: 'desc',
              },
            ],
            filters: [],
            columns: [
              {
                field: 'results→status',
                alias: 'status',
              },
              {
                field: 'results→due_date (Month)',
                skip: true,
                alias: 'due_date (Month)',
              },
              {
                field: 'results→Pending',
                skip: true,
                alias: 'Pending',
              },
              {
                field: 'results→On/Before',
                skip: true,
                alias: 'On/Before',
              },
              {
                field: 'results→After',
                skip: true,
                alias: 'After',
              },
              {
                field: 'results→Cumulative pending',
                skip: true,
                alias: 'Cumulative pending',
              },
              {
                field: 'results→Cumulative On/Before',
                skip: true,
                alias: 'Cumulative On/Before',
              },
              {
                field: 'results→Cumulative After',
                skip: true,
                alias: 'Cumulative After',
              },
              {
                field: 'results→Pending',
                agg: 'sum',
                skip: false,
                alias: 'PENDING',
              },
              {
                field: 'results→On/Before',
                agg: 'sum',
                skip: false,
                alias: 'ON TRACK',
              },
              {
                field: 'results→After',
                agg: 'sum',
                skip: false,
                alias: 'DELAYED',
              },
              {
                field: 'results→Cumulative pending',
                agg: 'max',
                alias: 'CUMULATIVE PENDING',
              },
              {
                field: 'results→Cumulative On/Before',
                agg: 'max',
                alias: 'CUMULATIVE ON TRACK',
              },
              {
                field: 'results→Cumulative After',
                agg: 'max',
                alias: 'CUMULATIVE DELAYED',
              },
            ],
          },
        ],
      },
    },
    name: 'NCR metrics - March',
  },
  'e16df1c3-f88a-4563-b5bd-c0646c54735e': {
    name: 'Progress report',
    config: {
      chart: {
        type: 'table',
        columns_map: {
          'NAME': {
            key: 'NAME',
            width: 246,
            visible: true,
            order_index: 0,
          },
          '% Weight': {
            key: '% Weight',
            width: null,
            visible: true,
            order_index: 1,
          },
          '% Planned': {
            key: '% Planned',
            width: null,
            visible: true,
            order_index: 2,
          },
          '% Actual': {
            key: '% Actual',
            width: null,
            visible: true,
            order_index: 3,
          },
          '% diff': {
            key: '% diff',
            width: 91,
            visible: true,
            order_index: 4,
          },
          '% Cu. planned': {
            key: '% Cu. planned',
            width: null,
            visible: true,
            order_index: 5,
          },
          '% Cu. Actual': {
            key: '% Cu. Actual',
            width: null,
            visible: true,
            order_index: 6,
          },
          'Cu. % diff': {
            key: 'Cu. % diff',
            width: null,
            visible: true,
            order_index: 7,
          },
        },
        show_row_headers: false,
        conditional_formatting: [
          {
            uid: 'dce2b8e1-dced-4306-8684-36723fe23fb2',
            field: '% diff',
            formatting_style: 'single_color',
            operator: 'less_than',
            apply_to: 'entire_row',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#F2726F',
            color_range: 7,
            value: '0',
          },
        ],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_timelines',
            orderBy: [],
            filters: [
              {
                field: 'activities→path',
                op: 'contains_any_of',
                value: [
                  'ENG-001',
                ],
              },
              {
                field: 'date (Month)',
                op: 'between',
                value: [
                  '2025-02-01T18:21:00.000Z',
                  '2025-02-28T18:21:00.000Z',
                ],
              },
            ],
            columns: [
              {
                field: 'progress_timelines→id',
                skip: true,
                alias: 'ID',
              },
              {
                field: 'activities→name',
                alias: 'NAME',
              },
              {
                expr: 'concat(round([activities→weight] * 100), "%")',
                type: 'column',
                alias: '% Weight',
              },
              {
                field: 'progress_timelines→date',
                dateBin: 'month',
                alias: 'date (Month)',
              },
              {
                field: 'progress_timelines→planned_progress',
                agg: 'sum',
                alias: 'Planned',
              },
              {
                field: 'progress_timelines→actual_progress',
                agg: 'sum',
                alias: 'Actual',
              },
              {
                field: 'progress_timelines→planned_progress',
                agg: 'cumsum',
                alias: 'Cum. Planned',
              },
              {
                field: 'progress_timelines→actual_progress',
                agg: 'cumsum',
                alias: 'Cum. Actual',
              },
            ],
            joins: [
              {
                table: {
                  name: 'activities',
                },
                label: 'activities',
                type: 'inner',
                on: [
                  {
                    left: 'progress_timelines→id',
                    left_label: 'id',
                    left_table: 'progress_timelines',
                    op: '=',
                    right: 'activities→id',
                    right_label: 'id',
                    right_table: 'activities',
                  },
                ],
              },
            ],
          },
          {
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'results→date (Month)',
                skip: true,
                alias: 'date (Month)',
              },
              {
                field: 'results→Planned',
                skip: true,
                alias: 'Planned',
              },
              {
                field: 'results→Actual',
                skip: true,
                alias: 'Actual',
              },
              {
                field: 'results→Cum. Planned',
                skip: true,
                alias: 'Cum. Planned',
              },
              {
                field: 'results→Cum. Actual',
                skip: true,
                alias: 'Cum. Actual',
              },
              {
                field: 'results→Planned',
                agg: 'sum',
                alias: '% Planned',
              },
              {
                field: 'results→Actual',
                agg: 'sum',
                alias: '% Actual',
              },
              {
                expr: '[% Actual] - [% Planned]',
                type: 'aggregation',
                alias: '% diff',
              },
              {
                field: 'results→Cum. Planned',
                agg: 'max',
                alias: '% Cu. planned',
              },
              {
                field: 'results→Cum. Actual',
                agg: 'max',
                alias: '% Cu. Actual',
              },
              {
                expr: '[% Cu. Actual] - [% Cu. planned]',
                type: 'aggregation',
                alias: 'Cu. % diff',
              },
            ],
          },
        ],
      },
    },
  },
  '8b3b8212-b091-4bcb-b5a8-1e33d6288c99': {
    name: 'Site Preparation & Clearing',
    config: {
      chart: {
        type: 'number_chart',
        columns_map: {
          id: {
            key: 'id',
            width: null,
            visible: true,
            order_index: 0,
          },
          name: {
            key: 'name',
            width: null,
            visible: true,
            order_index: 1,
          },
          planned_progress: {
            key: 'planned_progress',
            width: null,
            visible: true,
            order_index: 2,
          },
          actual_progress: {
            key: 'actual_progress',
            width: null,
            visible: true,
            order_index: 3,
          },
        },
        layout_category: 'actual_progress',
        layout_values: [
          {
            value: 'planned_progress',
          },
        ],
        show_percentage_change: true,
        switch_positive_negative_colors: false,
        compact: false,
        prefix: null,
        suffix: '%',
        number_widget_color: '#FFFFFF',
        stack_by: 'none',
        compare_by: 'difference',
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→id',
                op: 'in',
                value: [
                  'CONST-T001',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→id',
                alias: 'id',
              },
              {
                field: 'activities→name',
                alias: 'name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'planned_progress',
              },
              {
                field: 'activities→actual_progress',
                alias: 'actual_progress',
              },
            ],
          },
        ],
      },
    },
  },
  '4b23b37f-1a5f-4dfe-bfda-0055a02f5160': {
    config: {
      chart: {
        type: 'number_chart',
        columns_map: {
          id: {
            key: 'id',
            width: null,
            visible: true,
            order_index: 0,
          },
          name: {
            key: 'name',
            width: null,
            visible: true,
            order_index: 1,
          },
          planned_progress: {
            key: 'planned_progress',
            width: null,
            visible: true,
            order_index: 2,
          },
          actual_progress: {
            key: 'actual_progress',
            width: null,
            visible: true,
            order_index: 3,
          },
        },
        layout_category: 'actual_progress',
        layout_values: [
          {
            value: 'planned_progress',
          },
        ],
        show_percentage_change: true,
        switch_positive_negative_colors: false,
        compact: false,
        prefix: null,
        suffix: '%',
        number_widget_color: '#FFFFFF',
        stack_by: 'none',
        compare_by: 'difference',
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→id',
                op: 'in',
                value: [
                  'CONST-T002',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→id',
                alias: 'id',
              },
              {
                field: 'activities→name',
                alias: 'name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'planned_progress',
              },
              {
                field: 'activities→actual_progress',
                alias: 'actual_progress',
              },
            ],
          },
        ],
      },
    },
    name: 'Foundation Work',
  },
  '7682c345-ec21-47f3-a136-d99ccbb8ce34': {
    config: {
      chart: {
        type: 'number_chart',
        columns_map: {
          id: {
            key: 'id',
            width: null,
            visible: true,
            order_index: 0,
          },
          name: {
            key: 'name',
            width: null,
            visible: true,
            order_index: 1,
          },
          planned_progress: {
            key: 'planned_progress',
            width: null,
            visible: true,
            order_index: 2,
          },
          actual_progress: {
            key: 'actual_progress',
            width: null,
            visible: true,
            order_index: 3,
          },
        },
        layout_category: 'actual_progress',
        layout_values: [
          {
            value: 'planned_progress',
          },
        ],
        show_percentage_change: true,
        switch_positive_negative_colors: false,
        compact: false,
        prefix: null,
        suffix: '%',
        number_widget_color: '#FFFFFF',
        stack_by: 'none',
        compare_by: 'difference',
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→id',
                op: 'in',
                value: [
                  'CONST-T003',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→id',
                alias: 'id',
              },
              {
                field: 'activities→name',
                alias: 'name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'planned_progress',
              },
              {
                field: 'activities→actual_progress',
                alias: 'actual_progress',
              },
            ],
          },
        ],
      },
    },
    name: 'Mounting Structure Installation',
  },
  'f9491ded-a17f-4f6e-ad01-7ed5464f5f9e': {
    config: {
      chart: {
        type: 'number_chart',
        columns_map: {
          id: {
            key: 'id',
            width: null,
            visible: true,
            order_index: 0,
          },
          name: {
            key: 'name',
            width: null,
            visible: true,
            order_index: 1,
          },
          planned_progress: {
            key: 'planned_progress',
            width: null,
            visible: true,
            order_index: 2,
          },
          actual_progress: {
            key: 'actual_progress',
            width: null,
            visible: true,
            order_index: 3,
          },
        },
        layout_category: 'actual_progress',
        layout_values: [
          {
            value: 'planned_progress',
          },
        ],
        show_percentage_change: true,
        switch_positive_negative_colors: false,
        compact: false,
        prefix: null,
        suffix: '%',
        number_widget_color: '#FFFFFF',
        stack_by: 'none',
        compare_by: 'difference',
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→id',
                op: 'in',
                value: [
                  'CONST-T005',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→id',
                alias: 'id',
              },
              {
                field: 'activities→name',
                alias: 'name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'planned_progress',
              },
              {
                field: 'activities→actual_progress',
                alias: 'actual_progress',
              },
            ],
          },
        ],
      },
    },
    name: 'Electrical Installation & Wiring',
  },
  'aba45f19-0a5e-4460-9cbe-3ce38ef3f41e': {
    name: 'Key activities',
    config: {
      chart: {
        type: 'horizontalBar',
        data: {
          category: 'name',
          values: [
            'planned_progress',
            'actual_progress',
          ],
          stackBy: null,
        },
        interactions: {},
        series: {
          planned_progress: {
            name: 'Planned',
            type: 'bar',
            color: '#4778b8',
            stack: false,
            lineColor: '#4778b8',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          actual_progress: {
            name: 'Actual',
            type: 'bar',
            color: '#61B58F',
            stack: false,
            lineColor: '#61B58F',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '%',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→id',
                op: 'in',
                value: [
                  'ENG-001',
                  'PROC-001',
                  'CONST-001',
                  'COMM-001',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→name',
                alias: 'name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'planned_progress',
              },
              {
                field: 'activities→actual_progress',
                alias: 'actual_progress',
              },
            ],
          },
        ],
      },
    },
  },
  '290d5695-66e9-4b06-a701-c026aee51f2d': {
    name: 'Progress',
    config: {
      chart: {
        type: 'progress',
        data: {
          values: [
            'actual_progress',
          ],
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
        chartSpecific: {
          progress: {
            min: 0,
            max: 100,
            markers: [
              {
                label: 'Planned',
                value: 'planned_progress',
                color: '#F2726F',
              },
            ],
          },
        },
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→id',
                op: 'in',
                value: [
                  'PRJ-001',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→name',
                alias: 'name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'planned_progress',
              },
              {
                field: 'activities→actual_progress',
                alias: 'actual_progress',
              },
            ],
          },
        ],
      },
    },
  },
  'c128ee48-9320-4842-95eb-20732e03948e': {
    name: 'S-Curve',
    config: {
      chart: {
        type: 'mixed',
        data: {
          category: 'date (Month)',
          values: [
            'Planned',
            'Actual',
            'Cumulative planned',
            'Cumulative actual',
          ],
          stackBy: null,
        },
        interactions: {
          dataZoom: {
            enabled: false,
            type: 'both',
          },
        },
        series: {
          'Planned': {
            name: 'Planned',
            type: 'bar',
            yAxisIndex: 0,
            color: '#4778b8',
            stack: false,
            lineColor: '#4778b8',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Actual': {
            name: 'Actual',
            type: 'bar',
            yAxisIndex: 0,
            color: '#61B58F',
            stack: false,
            lineColor: '#61B58F',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cumulative planned': {
            name: 'Cumulative planned',
            type: 'line',
            yAxisIndex: 1,
            color: '#F2726F',
            stack: false,
            lineColor: '#F2726F',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cumulative actual': {
            name: 'Cumulative actual',
            type: 'line',
            yAxisIndex: 1,
            color: '#e9c45d',
            stack: false,
            lineColor: '#e9c45d',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: false,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          secondaryValueLabels: 'show',
          valueScale: 'linear',
          secondaryValueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: '2025_dates',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: '2025_dates→date',
                dateBin: 'month',
                alias: 'date (Month)',
              },
              {
                expr: '[activities→weight] * [progress_timelines→planned_progress]',
                type: 'column',
                skip: true,
                alias: 'planned',
              },
              {
                expr: '[activities→weight] * [progress_timelines→actual_progress]',
                type: 'column',
                skip: true,
                alias: 'actual',
              },
              {
                expr: 'Sum([planned])',
                type: 'aggregation',
                alias: 'Planned',
              },
              {
                expr: 'Sum([actual])',
                type: 'aggregation',
                alias: 'Actual',
              },
              {
                expr: 'CumulativeSum([planned])',
                type: 'aggregation',
                alias: 'Cumulative planned',
              },
              {
                expr: 'CumulativeSum([actual])',
                type: 'aggregation',
                alias: 'Cumulative actual',
              },
            ],
            joins: [
              {
                table: {
                  name: 'progress_timelines',
                },
                label: 'progress_timelines',
                type: 'left',
                on: [
                  {
                    left: '2025_dates→date',
                    left_label: 'date',
                    left_table: '2025_dates',
                    op: '=',
                    right: 'progress_timelines→date',
                    right_label: 'date',
                    right_table: 'progress_timelines',
                  },
                ],
              },
              {
                table: {
                  name: 'activities',
                },
                label: 'activities',
                type: 'left',
                on: [
                  {
                    left: 'progress_timelines→id',
                    left_label: 'id',
                    left_table: 'progress_timelines',
                    op: '=',
                    right: 'activities→id',
                    right_label: 'id',
                    right_table: 'activities',
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  },
  '1a92a351-0550-4ee0-83ee-e736990317c4': {
    config: {
      chart: {
        type: 'mixed',
        data: {
          category: 'date (Month)',
          values: [
            'Planned',
            'Actual',
            'Cumulative planned',
            'Cumulative actual',
          ],
          stackBy: null,
        },
        interactions: {
          dataZoom: {
            enabled: false,
            type: 'both',
          },
        },
        series: {
          'Planned': {
            name: 'Planned',
            type: 'bar',
            yAxisIndex: 0,
            color: '#4778b8',
            stack: false,
            lineColor: '#4778b8',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Actual': {
            name: 'Actual',
            type: 'bar',
            yAxisIndex: 0,
            color: '#61B58F',
            stack: false,
            lineColor: '#61B58F',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cumulative planned': {
            name: 'Cumulative planned',
            type: 'line',
            yAxisIndex: 1,
            color: '#F2726F',
            stack: false,
            lineColor: '#F2726F',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
          'Cumulative actual': {
            name: 'Cumulative actual',
            type: 'line',
            yAxisIndex: 1,
            color: '#e9c45d',
            stack: false,
            lineColor: '#e9c45d',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: false,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          secondaryValueLabels: 'show',
          valueScale: 'linear',
          secondaryValueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: '2025_dates',
            orderBy: [],
            filters: [
              {
                field: 'activities→path',
                op: 'contains_any_of',
                value: [
                  'CONST-001',
                ],
              },
            ],
            columns: [
              {
                field: '2025_dates→date',
                dateBin: 'month',
                alias: 'date (Month)',
              },
              {
                expr: '[activities→weight] * [progress_timelines→planned_progress]',
                type: 'column',
                skip: true,
                alias: 'planned',
              },
              {
                expr: '[activities→weight] * [progress_timelines→actual_progress]',
                type: 'column',
                skip: true,
                alias: 'actual',
              },
              {
                expr: 'Sum([planned])',
                type: 'aggregation',
                alias: 'Planned',
              },
              {
                expr: 'Sum([actual])',
                type: 'aggregation',
                alias: 'Actual',
              },
              {
                expr: 'CumulativeSum([planned])',
                type: 'aggregation',
                alias: 'Cumulative planned',
              },
              {
                expr: 'CumulativeSum([actual])',
                type: 'aggregation',
                alias: 'Cumulative actual',
              },
            ],
            joins: [
              {
                table: {
                  name: 'progress_timelines',
                },
                label: 'progress_timelines',
                type: 'left',
                on: [
                  {
                    left: '2025_dates→date',
                    left_label: 'date',
                    left_table: '2025_dates',
                    op: '=',
                    right: 'progress_timelines→date',
                    right_label: 'date',
                    right_table: 'progress_timelines',
                  },
                ],
              },
              {
                table: {
                  name: 'activities',
                },
                label: 'activities',
                type: 'left',
                on: [
                  {
                    left: 'progress_timelines→id',
                    left_label: 'id',
                    left_table: 'progress_timelines',
                    op: '=',
                    right: 'activities→id',
                    right_label: 'id',
                    right_table: 'activities',
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    name: 'Construction S-Curve',
  },
  'ca2d69f0-14bb-440f-bedb-399d50f1ba76': {
    name: 'Critical activities',
    config: {
      chart: {
        type: 'table',
        columns_map: {
          'ID': {
            key: 'ID',
            width: 322,
            visible: false,
            order_index: 0,
          },
          'Name': {
            key: 'Name',
            width: 325,
            visible: true,
            order_index: 1,
          },
          'Planned': {
            key: 'Planned',
            width: 153,
            visible: true,
            order_index: 2,
          },
          'Actual': {
            key: 'Actual',
            width: 97,
            visible: true,
            order_index: 3,
          },
          '% diff': {
            key: '% diff',
            width: 710,
            visible: true,
            order_index: 4,
          },
        },
        show_row_headers: false,
        conditional_formatting: [
          {
            uid: 'ec1642bf-8493-4ffd-b4f0-61dac1fd2895',
            field: '% diff',
            formatting_style: 'single_color',
            operator: 'less_than',
            value: '0',
            apply_to: 'entire_row',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#F2726F',
            color_range: 5,
          },
        ],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'activities',
            orderBy: [],
            filters: [
              {
                field: 'activities→path',
                op: 'contains_any_of',
                value: [
                  'CONST-001',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→id',
                alias: 'ID',
              },
              {
                field: 'activities→name',
                alias: 'Name',
              },
              {
                field: 'activities→planned_progress',
                alias: 'Planned',
              },
              {
                field: 'activities→actual_progress',
                alias: 'Actual',
              },
              {
                expr: '[Actual] - [Planned]',
                type: 'aggregation',
                alias: '% diff',
              },
            ],
          },
        ],
      },
    },
  },
  '8bc7a683-128f-4dc8-bcaf-90646a31fabf': {
    name: 'NCR metrics',
    config: {
      chart: {
        type: 'table',
        columns_map: {
          'Status': {
            key: 'Status',
            width: null,
            visible: true,
            order_index: 0,
          },
          'Pending': {
            key: 'Pending',
            width: 108,
            visible: true,
            order_index: 1,
          },
          'On/Before': {
            key: 'On/Before',
            width: 121,
            visible: true,
            order_index: 2,
          },
          'After': {
            key: 'After',
            width: 90,
            visible: true,
            order_index: 3,
          },
          'Total': {
            key: 'Total',
            width: 83,
            visible: true,
            order_index: 4,
          },
          'Cumulative pending': {
            key: 'Cumulative pending',
            width: 170,
            visible: true,
            order_index: 5,
          },
          'Cumulative on/before': {
            key: 'Cumulative on/before',
            width: 181,
            visible: true,
            order_index: 6,
          },
          'Cumulative after': {
            key: 'Cumulative after',
            width: 156,
            visible: true,
            order_index: 7,
          },
          'Cumulative total': {
            key: 'Cumulative total',
            width: 157,
            visible: true,
            order_index: 8,
          },
        },
        show_row_headers: false,
        conditional_formatting: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'submission_events_ncr',
            orderBy: [],
            filters: [
              {
                field: 'submission_events_ncr→status',
                op: 'in',
                value: [
                  'Execution',
                  'Issuance',
                ],
              },
            ],
            columns: [
              {
                field: 'submission_events_ncr→status',
                alias: 'Status',
              },
              {
                field: 'submission_events_ncr→due_date',
                dateBin: 'month',
                alias: 'due_date (Month)',
              },
              {
                expr: 'if(\n  isNull([submission_events_ncr→submitted_at]),\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'pending',
              },
              {
                expr: 'if(\n  [submission_events_ncr→submitted_at] <= [submission_events_ncr→due_date],\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'on/before',
              },
              {
                expr: 'if(\n  [submission_events_ncr→submitted_at] > [submission_events_ncr→due_date],\n  1,\n  0\n)',
                type: 'column',
                skip: true,
                alias: 'after',
              },
              {
                expr: 'Sum([pending])',
                type: 'aggregation',
                alias: 'Sum of pending',
              },
              {
                expr: 'Sum([on/before])',
                type: 'aggregation',
                alias: 'Sum of on/before',
              },
              {
                expr: 'Sum([after])',
                type: 'aggregation',
                alias: 'Sum of after',
              },
              {
                expr: 'CumulativeSum([pending])',
                type: 'aggregation',
                alias: 'Cumulative sum of pending',
              },
              {
                expr: 'CumulativeSum([on/before])',
                type: 'aggregation',
                alias: 'Cumulative sum of on/before',
              },
              {
                expr: 'CumulativeSum([after])',
                type: 'aggregation',
                alias: 'Cumulative sum of after',
              },
            ],
          },
          {
            orderBy: [
              {
                column: 'Status',
                direction: 'desc',
              },
            ],
            filters: [],
            columns: [
              {
                field: 'results→due_date (Month)',
                skip: true,
                alias: 'due_date (Month)',
              },
              {
                field: 'results→Sum of pending',
                skip: true,
                alias: 'Sum of pending',
              },
              {
                field: 'results→Sum of on/before',
                skip: true,
                alias: 'Sum of on/before',
              },
              {
                field: 'results→Sum of after',
                skip: true,
                alias: 'Sum of after',
              },
              {
                field: 'results→Cumulative sum of pending',
                skip: true,
                alias: 'Cumulative sum of pending',
              },
              {
                field: 'results→Cumulative sum of on/before',
                skip: true,
                alias: 'Cumulative sum of on/before',
              },
              {
                field: 'results→Cumulative sum of after',
                skip: true,
                alias: 'Cumulative sum of after',
              },
              {
                field: 'results→Sum of pending',
                agg: 'sum',
                alias: 'Pending',
              },
              {
                field: 'results→Sum of on/before',
                agg: 'sum',
                alias: 'On/Before',
              },
              {
                field: 'results→Sum of after',
                agg: 'sum',
                alias: 'After',
              },
              {
                expr: '[Pending] + [On/Before] + [After]',
                type: 'aggregation',
                alias: 'Total',
              },
              {
                field: 'results→Cumulative sum of pending',
                agg: 'max',
                alias: 'Cumulative pending',
              },
              {
                field: 'results→Cumulative sum of on/before',
                agg: 'max',
                alias: 'Cumulative on/before',
              },
              {
                field: 'results→Cumulative sum of after',
                agg: 'max',
                alias: 'Cumulative after',
              },
              {
                expr: '[Cumulative pending] + [Cumulative on/before] + [Cumulative after]',
                type: 'aggregation',
                alias: 'Cumulative total',
              },
            ],
          },
        ],
      },
    },
  },
  '6fbe5cc2-bc1d-409f-a1b1-fa1fe9c66c25': {
    name: 'Status breakdown',
    config: {
      chart: {
        type: 'donut',
        data: {
          category: 'Status',
          values: [
            'Count',
          ],
        },
        interactions: {},
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'forms_ncr',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'forms_ncr→status',
                alias: 'Status',
              },
              {
                field: 'forms_ncr→status',
                agg: 'count',
                alias: 'Count',
              },
            ],
          },
        ],
      },
    },
  },
  '970553a6-1fdd-4ef6-811a-899165abc967': {
    name: 'Priority vs status',
    config: {
      chart: {
        type: 'horizontalBar',
        data: {
          category: 'Priority',
          values: [
            'Count',
          ],
          stackBy: 'Status',
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        series: {
          Count: {
            name: 'Count',
            type: 'bar',
            color: 'distinct',
            stack: 'group1',
            lineColor: 'distinct',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'forms_ncr',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'forms_ncr→priority',
                alias: 'Priority',
              },
              {
                field: 'forms_ncr→status',
                alias: 'Status',
              },
              {
                field: 'forms_ncr→status',
                agg: 'count',
                alias: 'Count',
              },
            ],
          },
        ],
      },
    },
  },
  '61de6c3c-b459-4272-bec2-325a4d143724': {
    name: 'Incident types',
    config: {
      chart: {
        type: 'pyramid',
        data: {
          category: 'incident_type',
          values: [
            'Count of incident_type',
          ],
        },
        interactions: {},
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: false,
          position: 'hide',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
        chartSpecific: {
          pyramid: {
            labelsAtCenter: false,
            showPercentages: true,
            is3D: true,
          },
        },
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'hse_incidents',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'hse_incidents→incident_type',
                alias: 'incident_type',
              },
              {
                field: 'hse_incidents→incident_type',
                agg: 'count',
                alias: 'Count of incident_type',
              },
            ],
          },
        ],
      },
    },
  },
  'f5be8452-57f0-4de3-92f8-1031acd1b73f': {
    name: 'Defects leading to NCR',
    config: {
      chart: {
        type: 'pareto',
        data: {
          category: 'Defect',
          values: [
            'Count',
          ],
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
        },
        chartSpecific: {
          pareto: {
            show80PercentLine: true,
            eightyPercentLineColor: '#9481d5',
            eightyPercentLineStyle: 'dashed',
            barColor: '#6cbde0',
            lineColor: '#F2726F',
          },
        },
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'forms_ncr',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'forms_ncr→defect_type',
                alias: 'Defect',
              },
              {
                field: 'forms_ncr→defect_type',
                agg: 'count',
                alias: 'Count',
              },
            ],
          },
        ],
      },
    },
  },
  '5b6f0b00-b16b-4cd9-8cf3-ba7ed6065d04': {
    name: 'NCR submissions',
    config: {
      chart: {
        type: 'area',
        data: {
          category: 'Date',
          values: [
            'Count',
          ],
          stackBy: null,
        },
        interactions: {
          dataZoom: {
            enabled: false,
            type: 'both',
          },
        },
        series: {
          Count: {
            name: 'Count',
            type: 'area',
            yAxisIndex: 0,
            color: '#de6e9c',
            stack: false,
            lineColor: '#de6e9c',
            lineWidth: 2,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [
          {
            value: 'average',
            label: 'Average',
            color: '#e9e1b9',
            series: 'Count',
            lineStyle: 'dashed',
          },
        ],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'submission_events_ncr',
            orderBy: [],
            filters: [
              {
                field: 'submission_events_ncr→submitted_at',
                op: 'is_not_empty',
              },
            ],
            columns: [
              {
                field: 'submission_events_ncr→due_date',
                dateBin: 'week',
                alias: 'Date',
              },
              {
                field: 'submission_events_ncr→status',
                agg: 'count',
                alias: 'Count',
              },
            ],
          },
        ],
      },
    },
  },
  '7bf1118a-b5be-44e8-91d0-ba5885f6a377': {
    name: 'Defects breakdown',
    config: {
      chart: {
        type: 'pie',
        data: {
          category: 'Defect',
          values: [
            'Count of id',
          ],
        },
        interactions: {
          tooltip: {
            trigger: 'item',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
          prefix: '',
          suffix: '',
        },
        axes: {},
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'forms_ncr',
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'forms_ncr→defect_type',
                alias: 'Defect',
              },
              {
                field: 'forms_ncr→id',
                agg: 'count',
                alias: 'Count of id',
              },
            ],
          },
        ],
      },
    },
  },
  '76f649ae-0175-4310-aedc-89b35502cc0a': {
    name: 'Progress report',
    config: {
      chart: {
        type: 'table',
        columns_map: {
          'Name': {
            key: 'Name',
            width: 246,
            visible: true,
            order_index: 0,
          },
          '% Weight': {
            key: '% Weight',
            width: 116,
            visible: true,
            order_index: 1,
          },
          '% Planned': {
            key: '% Planned',
            width: 122,
            visible: true,
            order_index: 2,
          },
          '% Actual': {
            key: '% Actual',
            width: 116,
            visible: true,
            order_index: 3,
          },
          '% Diff': {
            key: '% Diff',
            width: 100,
            visible: true,
            order_index: 4,
          },
          '% Cumulative Planned': {
            key: '% Cumulative Planned',
            width: 201,
            visible: true,
            order_index: 5,
          },
          '% Cumulative Actual': {
            key: '% Cumulative Actual',
            width: 182,
            visible: true,
            order_index: 6,
          },
          '% Cumulative diff': {
            key: '% Cumulative diff',
            width: 166,
            visible: true,
            order_index: 7,
          },
        },
        show_row_headers: false,
        conditional_formatting: [
          {
            uid: 'd7dfb77f-d85d-49ab-a964-465908118d2e',
            field: '% Diff',
            formatting_style: 'single_color',
            operator: 'less_than',
            apply_to: 'entire_row',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#F2726F',
            color_range: 5,
            value: '0',
          },
          {
            uid: '7d74085f-6d86-4d52-ae6c-60b3ccd756fb',
            field: '% Diff',
            formatting_style: 'single_color',
            operator: 'greater_than',
            value: '0',
            apply_to: 'entire_row',
            start_range_at: 'min',
            start_range_value: null,
            end_range_at: 'max',
            end_range_value: null,
            color: '#b0d364',
            color_range: 5,
          },
        ],
      },
      query: {
        selected_database: 'grs_corvera',
        stages: [
          {
            table: 'progress_timelines',
            orderBy: [],
            filters: [
              {
                field: 'activities→path',
                op: 'contains_any_of',
                value: [
                  'ENG-001',
                ],
              },
              {
                field: 'Month',
                op: 'between',
                value: [
                  '2025-02-01T16:13:00.000Z',
                  '2025-02-28T16:13:00.000Z',
                ],
              },
            ],
            columns: [
              {
                field: 'activities→name',
                alias: 'Name',
              },
              {
                expr: '[activities→weight] * 100',
                type: 'column',
                alias: '% Weight',
              },
              {
                field: 'progress_timelines→date',
                dateBin: 'month',
                skip: false,
                alias: 'Month',
              },
              {
                field: 'progress_timelines→planned_progress',
                agg: 'sum',
                alias: 'Planned',
              },
              {
                field: 'progress_timelines→actual_progress',
                agg: 'sum',
                alias: 'Actual',
              },
              {
                field: 'progress_timelines→planned_progress',
                agg: 'cumsum',
                alias: 'Cumulative Planned',
              },
              {
                field: 'progress_timelines→actual_progress',
                agg: 'cumsum',
                alias: 'Cumulative Actual',
              },
            ],
            joins: [
              {
                table: {
                  name: 'activities',
                },
                label: 'activities',
                type: 'inner',
                on: [
                  {
                    left: 'progress_timelines→id',
                    left_label: 'id',
                    left_table: 'progress_timelines',
                    op: '=',
                    right: 'activities→id',
                    right_label: 'id',
                    right_table: 'activities',
                  },
                ],
              },
            ],
          },
          {
            orderBy: [],
            filters: [],
            columns: [
              {
                field: 'results→Month',
                skip: true,
                alias: 'Month',
              },
              {
                field: 'results→Planned',
                skip: true,
                alias: 'Planned',
              },
              {
                field: 'results→Actual',
                skip: true,
                alias: 'Actual',
              },
              {
                field: 'results→Cumulative Planned',
                skip: true,
                alias: 'Cumulative Planned',
              },
              {
                field: 'results→Cumulative Actual',
                skip: true,
                alias: 'Cumulative Actual',
              },
              {
                field: 'results→Planned',
                agg: 'sum',
                alias: '% Planned',
              },
              {
                field: 'results→Actual',
                agg: 'sum',
                alias: '% Actual',
              },
              {
                expr: '[% Actual] - [% Planned]',
                type: 'aggregation',
                alias: '% Diff',
              },
              {
                field: 'results→Cumulative Planned',
                agg: 'max',
                alias: '% Cumulative Planned',
              },
              {
                field: 'results→Cumulative Actual',
                agg: 'max',
                alias: '% Cumulative Actual',
              },
              {
                expr: '[% Cumulative Actual] - [% Cumulative Planned]',
                type: 'aggregation',
                alias: '% Cumulative diff',
              },
            ],
          },
        ],
      },
    },
  },
};

const DEFAULT_DASHBOARDS = {
  '1': {
    uid: '1',
    name: 'Dashboard 1',
    widgets: [
      {
        x: 0,
        y: 25,
        w: 12,
        h: 10,
        i: '6',
        widget_id: '804a24d2-1ae3-42c6-929a-40e1a95b8c6f',
        moved: false,
      },
      {
        x: 6,
        y: 0,
        w: 6,
        h: 11,
        i: '7',
        widget_id: 'fc6cf328-a945-4c96-9ffe-35906b9a4c67',
        moved: false,
      },
      {
        x: 0,
        y: 35,
        w: 7,
        h: 15,
        i: '8',
        widget_id: '70248e64-e52c-4dc7-8868-a9536dbc9406',
        moved: false,
      },
      {
        x: 7,
        y: 35,
        w: 5,
        h: 10,
        i: '9',
        widget_id: '918b25ab-14d1-482a-bb55-98ebf4e33ce1',
        moved: false,
      },
      {
        x: 7,
        y: 45,
        w: 2,
        h: 3,
        i: '10',
        widget_id: '92e284d3-2677-469e-9d16-9da9abce1fae',
        moved: false,
      },
      {
        x: 0,
        y: 11,
        w: 6,
        h: 14,
        i: '12',
        widget_id: '4f44a067-f77d-49bb-9b63-df59417d0870',
        moved: false,
      },
      {
        x: 6,
        y: 11,
        w: 6,
        h: 14,
        i: '13',
        widget_id: '474be9be-e7dc-4631-a5cc-77c14c06a012',
        moved: false,
      },
      {
        x: 0,
        y: 0,
        w: 6,
        h: 11,
        i: '14',
        widget_id: '05bf089a-2420-4e6b-ad5d-8f9ed0d1fe94',
        moved: false,
      },
      {
        x: 0,
        y: 51,
        w: 7,
        h: 13,
        i: '15',
        widget_id: 'd5e063f4-ec2e-443b-bd94-6bc91911adb4',
        moved: false,
      },
      {
        x: 0,
        y: 64,
        w: 10,
        h: 5,
        i: '18',
        widget_id: '8999ee1e-1f5c-4b7c-a003-c64894ca03e7',
        moved: false,
      },
      {
        x: 0,
        y: 69,
        w: 10,
        h: 5,
        i: '19',
        widget_id: '66f0e03f-e916-4640-83bf-f12ccea5f380',
        moved: false,
      },
      {
        x: 0,
        y: 74,
        w: 9,
        h: 7,
        i: '20',
        widget_id: 'e16df1c3-f88a-4563-b5bd-c0646c54735e',
        moved: false,
      },
      {
        x: 0,
        y: 50,
        w: 12,
        h: 1,
        i: '21',
        widget_id: '0805de09-b5c8-42ff-9cde-9ce1368e8f29',
        moved: false,
      },
    ],
  },
  '2f60888a-9f83-4315-a34c-a2c8e0687742': {
    uid: '2f60888a-9f83-4315-a34c-a2c8e0687742',
    name: 'TotalEnergies demo',
    widgets: [
      {
        x: 0,
        y: 15,
        w: 12,
        h: 1,
        i: '5',
        widget_id: 'c30d2216-e4c3-40f6-95ca-a3c40093e028',
        moved: false,
      },
      {
        x: 0,
        y: 16,
        w: 3,
        h: 3,
        i: '1',
        widget_id: '8b3b8212-b091-4bcb-b5a8-1e33d6288c99',
        moved: false,
      },
      {
        x: 3,
        y: 16,
        w: 3,
        h: 3,
        i: '2',
        widget_id: '4b23b37f-1a5f-4dfe-bfda-0055a02f5160',
        moved: false,
      },
      {
        x: 6,
        y: 16,
        w: 3,
        h: 3,
        i: '3',
        widget_id: '7682c345-ec21-47f3-a136-d99ccbb8ce34',
        moved: false,
      },
      {
        x: 9,
        y: 16,
        w: 3,
        h: 3,
        i: '4',
        widget_id: 'f9491ded-a17f-4f6e-ad01-7ed5464f5f9e',
        moved: false,
      },
      {
        x: 0,
        y: 6,
        w: 4,
        h: 9,
        i: '6',
        widget_id: 'aba45f19-0a5e-4460-9cbe-3ce38ef3f41e',
        moved: false,
      },
      {
        x: 0,
        y: 1,
        w: 4,
        h: 5,
        i: '7',
        widget_id: '290d5695-66e9-4b06-a701-c026aee51f2d',
        moved: false,
      },
      {
        x: 4,
        y: 1,
        w: 8,
        h: 14,
        i: '8',
        widget_id: 'c128ee48-9320-4842-95eb-20732e03948e',
        moved: false,
      },
      {
        x: 0,
        y: 0,
        w: 12,
        h: 1,
        i: '9',
        widget_id: '4ccbb330-fc84-4a26-836b-e9e6fe673994',
        moved: false,
      },
      {
        x: 6,
        y: 19,
        w: 6,
        h: 10,
        i: '10',
        widget_id: '1a92a351-0550-4ee0-83ee-e736990317c4',
        moved: false,
      },
      {
        x: 0,
        y: 19,
        w: 6,
        h: 10,
        i: '11',
        widget_id: 'ca2d69f0-14bb-440f-bedb-399d50f1ba76',
        moved: false,
      },
      {
        x: 0,
        y: 47,
        w: 12,
        h: 5,
        i: '13',
        widget_id: '8bc7a683-128f-4dc8-bcaf-90646a31fabf',
        moved: false,
      },
      {
        x: 0,
        y: 36,
        w: 12,
        h: 1,
        i: '14',
        widget_id: '42cfd15b-433e-4f30-8aa3-a45f8898f73f',
        moved: false,
      },
      {
        x: 0,
        y: 37,
        w: 5,
        h: 10,
        i: '15',
        widget_id: '6fbe5cc2-bc1d-409f-a1b1-fa1fe9c66c25',
        moved: false,
      },
      {
        x: 5,
        y: 37,
        w: 7,
        h: 10,
        i: '16',
        widget_id: '970553a6-1fdd-4ef6-811a-899165abc967',
        moved: false,
      },
      {
        x: 7,
        y: 52,
        w: 5,
        h: 11,
        i: '17',
        widget_id: '61de6c3c-b459-4272-bec2-325a4d143724',
        moved: false,
      },
      {
        x: 0,
        y: 52,
        w: 7,
        h: 11,
        i: '18',
        widget_id: 'f5be8452-57f0-4de3-92f8-1031acd1b73f',
        moved: false,
      },
      {
        x: 5,
        y: 63,
        w: 7,
        h: 10,
        i: '19',
        widget_id: '5b6f0b00-b16b-4cd9-8cf3-ba7ed6065d04',
        moved: false,
      },
      {
        x: 0,
        y: 63,
        w: 5,
        h: 10,
        i: '20',
        widget_id: '7bf1118a-b5be-44e8-91d0-ba5885f6a377',
        moved: false,
      },
      {
        x: 0,
        y: 29,
        w: 12,
        h: 7,
        i: '21',
        widget_id: '76f649ae-0175-4310-aedc-89b35502cc0a',
        moved: false,
      },
    ],
  },
};

export function useBILocalStorage() {
  function safeParseJSON(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    }
    catch {
      return defaultValue;
    }
  }

  function safeSetJSON(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    }
    catch {
      return false;
    }
  }

  function initializeStorage() {
    const existing_dashboards = safeParseJSON(BI_STORAGE_KEYS.DASHBOARDS);
    const existing_widgets = safeParseJSON(BI_STORAGE_KEYS.WIDGETS);

    if (!existing_dashboards) {
      const dashboards_obj = DEFAULT_DASHBOARDS;
      safeSetJSON(BI_STORAGE_KEYS.DASHBOARDS, dashboards_obj);
    }

    if (!existing_widgets) {
      safeSetJSON(BI_STORAGE_KEYS.WIDGETS, DEFAULT_WIDGETS_DATA);
    }
  }

  // Dashboard operations
  function getDashboards() {
    return safeParseJSON(BI_STORAGE_KEYS.DASHBOARDS, {});
  }

  function saveDashboards(dashboards) {
    return safeSetJSON(BI_STORAGE_KEYS.DASHBOARDS, dashboards);
  }

  function createDashboard(dashboard) {
    const dashboards = getDashboards();
    dashboards[dashboard.uid] = dashboard;
    return saveDashboards(dashboards);
  }

  function updateDashboard(uid, updates) {
    const dashboards = getDashboards();
    if (dashboards[uid]) {
      dashboards[uid] = { ...dashboards[uid], ...updates };
      return saveDashboards(dashboards);
    }
    return false;
  }

  function deleteDashboard(uid) {
    const dashboards = getDashboards();
    if (dashboards[uid]) {
      delete dashboards[uid];
      return saveDashboards(dashboards);
    }
    return false;
  }

  // Widget operations
  function getWidgets() {
    return safeParseJSON(BI_STORAGE_KEYS.WIDGETS, {});
  }

  function saveWidgets(widgets) {
    return safeSetJSON(BI_STORAGE_KEYS.WIDGETS, widgets);
  }

  function createWidget(widget_id, widget_data) {
    const widgets = getWidgets();
    widgets[widget_id] = widget_data;
    return saveWidgets(widgets);
  }

  function updateWidget(widget_id, updates) {
    const widgets = getWidgets();
    if (widgets[widget_id]) {
      widgets[widget_id] = { ...widgets[widget_id], ...updates };
      return saveWidgets(widgets);
    }
    return false;
  }

  function deleteWidget(widget_id) {
    const widgets = getWidgets();
    if (widgets[widget_id]) {
      delete widgets[widget_id];
      return saveWidgets(widgets);
    }
    return false;
  }

  function clearAllData() {
    Object.values(BI_STORAGE_KEYS).forEach((key) => {
      localStorage.removeItem(key);
    });
    window.location.reload();
  }

  return {
    // Initialization
    initializeStorage,

    // Dashboard operations
    getDashboards,
    saveDashboards,
    createDashboard,
    updateDashboard,
    deleteDashboard,

    // Widget operations
    getWidgets,
    saveWidgets,
    createWidget,
    updateWidget,
    deleteWidget,

    // Utility
    clearAllData,
  };
}
