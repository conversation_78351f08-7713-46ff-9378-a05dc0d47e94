import BIBetweenInputDate from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-between-input/bi-between-input-date.vue';
import BIBetweenInputNumber from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-between-input/bi-between-input-number.vue';
import BIInputDate from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-input/bi-input-date.vue';
import BIInputNumber from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-input/bi-input-number.vue';
import BIInputText from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-input/bi-input-text.vue';
import BIMultiSelectCheckbox from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-multi-select/bi-multi-select-checkbox.vue';
import BINone from '~/bi/components/filters/bi-filter-list/bi-filter/operator-input-components/bi-none.vue';
import BIAnchorDate from '../components/filters/bi-filter-list/bi-filter/operator-input-components/bi-anchor/bi-anchor-date.vue';
import BIAnchorPeriod from '../components/filters/bi-filter-list/bi-filter/operator-input-components/bi-anchor/bi-anchor-period.vue';

export function useBIFilterMaps() {
  const operators_config_map = {
    in: {
      name: 'in',
      label: 'is',
      description: 'Value is in the specified list',
      supportedTypes: [
        'text',
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      valueType: 'array',
      component: 'multi_select',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            'active',
            'pending',
          ],
          description: 'Status is active or pending',
        },
      ],
    },
    not_in: {
      name: 'not_in',
      label: 'is not',
      description: 'Value is not in the specified list',
      supportedTypes: [
        'text',
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      valueType: 'array',
      component: 'multi_select',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            'draft',
            'archived',
          ],
          description: 'Status is not draft or archived',
        },
      ],
    },
    equals: {
      name: 'equals',
      label: 'equals',
      description: 'Value equals the specified value',
      supportedTypes: [
        'text',
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: true,
      examples: [
        {
          value: 'active',
          description: 'Status equals \'active\'',
        },
        {
          value: [
            'active',
            'pending',
          ],
          description: 'Status equals \'active\' or \'pending\'',
        },
        {
          value: 100,
          description: 'Amount equals 100',
        },
        {
          value: [
            100,
            200,
          ],
          description: 'Amount equals 100 or 200',
        },
        {
          value: '2024-01-01',
          description: 'Date equals January 1, 2024',
        },
      ],
    },
    does_not_equal: {
      name: 'does_not_equal',
      label: 'not equals',
      description: 'Value does not equal the specified value',
      supportedTypes: [
        'text',
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: 'inactive',
          description: 'Status does not equal \'inactive\'',
        },
        {
          value: 0,
          description: 'Amount does not equal 0',
        },
      ],
    },
    greater_than: {
      name: 'greater_than',
      label: 'greater than',
      description: 'Value is greater than the specified number',
      supportedTypes: [
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: 100,
          description: 'Amount greater than 100',
        },
        {
          value: '2024-01-01',
          description: 'Date after January 1, 2024',
        },
      ],
    },
    greater_than_or_equal_to: {
      name: 'greater_than_or_equal_to',
      label: 'greater than or equal to',
      description: 'Value is greater than or equal to the specified number',
      supportedTypes: [
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: 100,
          description: 'Amount greater than or equal to 100',
        },
        {
          value: '2024-01-01',
          description: 'Date on or after January 1, 2024',
        },
      ],
    },
    less_than: {
      name: 'less_than',
      label: 'less than',
      description: 'Value is less than the specified number',
      supportedTypes: [
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: 1000,
          description: 'Amount less than 1000',
        },
      ],
    },
    less_than_or_equal_to: {
      name: 'less_than_or_equal_to',
      label: 'less than or equal to',
      description: 'Value is less than or equal to the specified number',
      supportedTypes: [
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: 1000,
          description: 'Amount less than or equal to 1000',
        },
        {
          value: '2024-12-31',
          description: 'Date on or before December 31, 2024',
        },
      ],
    },
    between: {
      name: 'between',
      label: 'between',
      description: 'Value is between the specified start and end values (inclusive)',
      supportedTypes: [
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'range_input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            100,
            500,
          ],
          description: 'Amount between 100 and 500',
        },
        {
          value: [
            '2024-01-01',
            '2024-12-31',
          ],
          description: 'Date between January 1, 2024 and December 31, 2024',
        },
      ],
    },
    not_between: {
      name: 'not_between',
      label: 'not between',
      description: 'Value is not between the specified start and end values',
      supportedTypes: [
        'integer',
        'float',
        'date',
        'timestamp',
      ],
      component: 'range_input',
      valueType: 'same_as_field',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            100,
            500,
          ],
          description: 'Amount not between 100 and 500',
        },
        {
          value: [
            '2024-01-01',
            '2024-12-31',
          ],
          description: 'Date not in 2024',
        },
      ],
    },
    is_empty: {
      name: 'is_empty',
      label: 'is empty',
      description: 'Field is null or empty (for text fields, checks both null and empty string)',
      supportedTypes: [
        'text',
        'integer',
        'float',
        'boolean',
        'date',
        'timestamp',
      ],
      valueType: 'none',
      component: 'none',
      valueRequired: false,
      supportsMultipleValues: false,
      examples: [
        {
          value: null,
          description: 'Description is empty',
        },
      ],
    },
    is_not_empty: {
      name: 'is_not_empty',
      label: 'is not empty',
      description: 'Field has a value (for text fields, not null and not empty string)',
      supportedTypes: [
        'text',
        'integer',
        'float',
        'boolean',
        'date',
        'timestamp',
      ],
      valueType: 'none',
      component: 'none',
      valueRequired: false,
      supportsMultipleValues: false,
      examples: [
        {
          value: null,
          description: 'Description is not empty',
        },
      ],
    },
    starts_with: {
      name: 'starts_with',
      label: 'starts with',
      description: 'Text starts with the specified string',
      supportedTypes: [
        'text',
      ],
      valueType: 'same_as_field',
      component: 'input',
      valueRequired: true,
      supportsMultipleValues: false,
      options: {
        caseSensitive: {
          label: 'Case Sensitive',
          type: 'checkbox',
          default: false,
          valueMapping: {
            true: 'starts_with',
            false: 'istarts_with',
          },
        },
      },
      examples: [
        {
          value: 'Mr',
          description: 'Name starts with \'Mr\'',
        },
      ],
    },
    ends_with: {
      name: 'ends_with',
      label: 'ends with',
      description: 'Text ends with the specified string',
      supportedTypes: [
        'text',
      ],
      valueType: 'same_as_field',
      component: 'input',
      valueRequired: true,
      supportsMultipleValues: false,
      options: {
        caseSensitive: {
          label: 'Case Sensitive',
          type: 'checkbox',
          default: false,
          valueMapping: {
            true: 'ends_with',
            false: 'iends_with',
          },
        },
      },
      examples: [
        {
          value: 'Jr',
          description: 'Name ends with \'Jr\'',
        },
      ],
    },
    contains: {
      name: 'contains',
      label: 'contains',
      description: 'Text contains the specified substring',
      supportedTypes: [
        'text',
      ],
      valueType: 'same_as_field',
      component: 'input',
      valueRequired: true,
      supportsMultipleValues: false,
      options: {
        caseSensitive: {
          label: 'Case Sensitive',
          type: 'checkbox',
          default: false,
          valueMapping: {
            true: 'contains',
            false: 'icontains',
          },
        },
      },
      examples: [
        {
          value: 'john',
          description: 'Name contains \'john\'',
        },
      ],
    },
    does_not_contain: {
      name: 'does_not_contain',
      label: 'does not contain',
      description: 'Text does not contain the specified substring',
      supportedTypes: [
        'text',
      ],
      valueType: 'same_as_field',
      component: 'input',
      valueRequired: true,
      supportsMultipleValues: false,
      options: {
        caseSensitive: {
          label: 'Case Sensitive',
          type: 'checkbox',
          default: false,
          valueMapping: {
            true: 'does_not_contain',
            false: 'idoes_not_contain',
          },
        },
      },
      examples: [
        {
          value: 'test',
          description: 'Name does not contain \'test\'',
        },
      ],
    },
    contains_any_of: {
      name: 'contains_any_of',
      label: 'contains any of',
      description: 'Array contains at least one of the specified values',
      supportedTypes: [
        'array',
      ],
      valueType: 'array',
      component: 'multi_select',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            'tag1',
            'tag2',
          ],
          description: 'Tags contains \'tag1\' or \'tag2\'',
        },
      ],
    },
    contains_all_of: {
      name: 'contains_all_of',
      label: 'contains all of',
      description: 'Array contains all of the specified values',
      supportedTypes: [
        'array',
      ],
      valueType: 'array',
      component: 'multi_select',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            'tag1',
            'tag2',
          ],
          description: 'Tags contains both \'tag1\' and \'tag2\'',
        },
      ],
    },
    contains_exactly: {
      name: 'contains_exactly',
      label: 'contains exactly',
      description: 'Array contains exactly the specified values in any order',
      supportedTypes: [
        'array',
      ],
      valueType: 'array',
      component: 'multi_select',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            'tag1',
            'tag2',
          ],
          description: 'Tags contains exactly \'tag1\' and \'tag2\'',
        },
      ],
    },
    contains_none_of: {
      name: 'contains_none_of',
      label: 'contains none of',
      description: 'Array contains none of the specified values',
      supportedTypes: [
        'array',
      ],
      valueType: 'array',
      component: 'multi_select',
      valueRequired: true,
      supportsMultipleValues: false,
      examples: [
        {
          value: [
            'tag1',
            'tag2',
          ],
          description: 'Tags contains none of \'tag1\' and \'tag2\'',
        },
      ],
    },
    n_to_date: {
      name: 'n_to_date',
      label: 'x to date',
      description: 'Date falls within current N to date (Monday to today). Supports dynamic anchor dates with sign control: "+7_days" (future), "-7_days" (past), and extended units: days, weeks, months, quarters, years',
      supportedTypes: [
        'date',
        'timestamp',
      ],
      valueType: 'text',
      component: 'anchor_date',
      valueRequired: false,
      options: {
        unit: {
          label: 'Unit',
          type: 'select',
          options: [
            'day',
            'week',
            'month',
            'quarter',
            'year',
          ],
          default: 'week',
          valueMapping: {
            week: 'week_to_date',
            month: 'month_to_date',
            quarter: 'quarter_to_date',
            year: 'year_to_date',
          },
        },
      },
      supportsMultipleValues: false,
      examples: [
        {
          op: 'week_to_date',
          value: null,
          description: 'Monday to today',
        },
        {
          op: 'month_to_date',
          value: '2024-07-15',
          description: '1st of July 15 to July 15',
        },
        {
          op: 'quarter_to_date',
          value: '2_weeks',
          description: 'Quarter start 2 weeks ago to 2 weeks ago',
        },
        {
          op: 'year_to_date',
          value: '-30_days',
          description: 'Jan 1st 30 days ago to 30 days ago',
        },
      ],
    },
    relative_period: {
      name: 'relative_period',
      label: 'relative period',
      description: 'Date falls within a relative period (with optional anchor date). Supports dynamic anchor dates with sign control: "+7_days" (future), "-7_days" (past), and extended units: days, weeks, months, quarters, years',
      supportedTypes: [
        'date',
        'timestamp',
      ],
      valueType: 'text',
      component: 'anchor_period',
      valueRequired: true,
      supportsMultipleValues: false,
      options: {
        direction: {
          label: 'Direction',
          type: 'select',
          options: [
            'last',
            'current',
            'next',
          ],
          default: 'last',
          valueMapping: {
            last: 'relative_date_last',
            current: 'relative_date_current',
            next: 'relative_date_next',
          },
        },
      },
      examples: [
        {
          value: '-7_days',
          description: 'Last 7 days from today',
        },
        {
          value: 'week',
          description: 'Last complete week',
        },
        {
          value: [
            '-30_days',
            '2024-07-15',
          ],
          description: 'Last 30 days from July 15, 2024',
        },
      ],
    },
  };

  // Contains mapping of value types and field types to components and their configurations
  const value_type_map_field_component = {
    input: {
      text: {
        component: BIInputText,
        componentConfig: {},
      },
      integer: {
        component: BIInputNumber,
        componentConfig: {},
      },
      float: {
        component: BIInputNumber,
        componentConfig: {},
      },
      date: {
        component: BIInputDate,
        componentConfig: {
          options: {
            placeholder: 'Select date',
            format: 'dd-MM-yyyy',
            range: false,
          },
        },
      },
      timestamp: {
        component: BIInputDate,
        componentConfig: {
          options: {
            placeholder: 'Select date',
            range: false,
            format: 'dd-MM-yyyy h:mm a',
            // timePicker: true,
            is24: false,
            enableTimePicker: true,
          },
        },
      },
    },
    multi_select: {
      text: {
        component: BIMultiSelectCheckbox,
        componentConfig: {},
      },
      integer: {
        component: BIMultiSelectCheckbox,
        componentConfig: {},
      },
      float: {
        component: BIMultiSelectCheckbox,
        componentConfig: {},
      },
      date: {
        component: BIMultiSelectCheckbox,
        componentConfig: {},
      },
      timestamp: {
        component: BIMultiSelectCheckbox,
        componentConfig: {},
      },
      array: {
        component: BIMultiSelectCheckbox,
        componentConfig: {},
      },
    },
    range_input: {
      integer: {
        component: BIBetweenInputNumber,
        componentConfig: {},
      },
      date: {
        component: BIBetweenInputDate,
        componentConfig: {
          options: {
            min_options: {
              placeholder: 'Start date',
              range: false,
              format: 'dd-MM-yyyy',
              // enableTimePicker: true,
            },
            max_options: {
              placeholder: 'End date',
              range: false,
              format: 'dd-MM-yyyy',
              // enableTimePicker: true,
            },
          },
        },
      },
      timestamp: {
        component: BIBetweenInputDate,
        componentConfig: {
          options: {
            min_options: {
              placeholder: 'Start date',
              range: false,
              format: 'dd-MM-yyyy h:mm a',
              // timePicker: true,
              is24: false,
              enableTimePicker: true,
            },
            max_options: {
              placeholder: 'End date',
              range: false,
              format: 'dd-MM-yyyy h:mm a',
              // timePicker: true,
              is24: false,
              enableTimePicker: true,
            },
          },
        },
      },
    },
    anchor_date: {
      date: {
        component: BIAnchorDate,
        componentConfig: {},
      },
      timestamp: {
        component: BIAnchorDate,
        componentConfig: {},
      },
    },
    anchor_period: {
      date: {
        component: BIAnchorPeriod,
        componentConfig: {},
      },
      timestamp: {
        component: BIAnchorPeriod,
        componentConfig: {},
      },
    },
  };

  function getTypeOperatorMap() {
    return Object.entries(operators_config_map).reduce((acc, [_key, config]) => {
      config.supportedTypes.forEach((type) => {
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(config.name);
      });
      return acc;
    }, {});
  }

  function getOperatorComponent({ selected_operator, selected_column_config, column_options }) {
    // console.log('Get Operator Component:', selected_operator, selected_column_config, column_options);
    // console.log(selected_operator?.valueType === 'none', selected_operator?.valueType);
    if (selected_operator?.valueType === 'none') {
      return {
        component: BINone,
        componentConfig: {},
      };
    }
    else {
      const component_data = value_type_map_field_component[selected_operator?.component]?.[selected_column_config.type];

      if (!component_data?.columnConfig) {
        component_data.columnConfig = {};
      }
      if (!component_data?.operatorConfig) {
        component_data.operatorConfig = {};
      }
      component_data.columnConfig = { ...selected_column_config };
      component_data.operatorConfig = { ...selected_operator };

      if (selected_operator?.valueType === 'array') {
        component_data.columnConfig.options = column_options;
      }

      return value_type_map_field_component[selected_operator?.component]?.[selected_column_config.type];
    }
  }

  return {
    operators_config_map,
    getTypeOperatorMap,
    getOperatorComponent,
  };
}
