import { isNil } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { computed, nextTick } from 'vue';
import { AXES_NAMES_SUPPORTED_CHARTS, BI_DEFAULT_PALETTE_COLORS, BI_HEATMAP_PALETTES, CHART_TO_VALUE_TYPE_MAP, DUAL_Y_AXIS_SUPPORTED_CHARTS, SERIES_SUPPORTED_CHARTS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

export function useBiChartBuilderHelpers() {
  const bi_store = useBiStore();
  const { widget_data, chart_builder_columns_data_types, widget_builder_config, is_chart_builder_form_valid } = storeToRefs(bi_store);

  const all_columns = computed(() => {
    return Object.keys(widget_data.value[0]);
  });

  const stack_by_field_columns = computed(() => {
    return all_columns.value.filter((column) => {
      return chart_builder_columns_data_types.value[column] === 'text';
    });
  });

  const markers_value_options = computed(() => {
    return all_columns.value.filter((column) => {
      if (widget_builder_config.value.chart.layout_values === column)
        return false;
      if (CHART_TO_VALUE_TYPE_MAP[widget_builder_config.value.chart.type].includes(chart_builder_columns_data_types.value[column]))
        return true;
      return false;
    });
  });

  const date_bin_units = computed(() => {
    let category_column;
    widget_builder_config.value.query.stages.forEach((stage) => {
      if (!category_column)
        category_column = stage.columns.find(column => column.alias === widget_builder_config.value.chart.layout_category);
    });

    return category_column?.dateBin || category_column?.oldDateBin || 'day';
  });

  const date_bin_label = computed(() => {
    const bin_to_label_map = {
      day: 'days',
      week: 'weeks',
      month: 'months',
      quarter: 'quarters',
      year: 'years',
    };
    return bin_to_label_map[date_bin_units.value];
  });

  const min_value_for_gauge_and_progress = computed(() => {
    return Math.min(Number(widget_data.value[0]?.[widget_builder_config.value.chart.layout_values]), 0);
  });

  const max_value_for_gauge_and_progress = computed(() => {
    return Math.max(Number(widget_data.value[0]?.[widget_builder_config.value.chart.layout_values]) * 1.5, 100);
  });

  async function convertFormDataToConfig(form_config, chart_instance) {
    const config = {
      type: form_config.chart.type.replace('_chart', ''),
    };

    // Layout tab
    const values = Array.isArray(form_config.chart.layout_values)
      ? form_config.chart.layout_values
      : [{ value: form_config.chart.layout_values }];
    if (!is_chart_builder_form_valid.value) {
      chart_instance?.dispose?.();
      return;
    }
    await nextTick();
    config.data = {
      category: form_config.chart.layout_category,
      values: values.map(item => item.value),
      ...(
        (['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'heatmap_chart'].includes(form_config.chart.type))
          ? {
              stackBy: form_config.chart.stack_by === 'none' ? null : form_config.chart.stack_by,
            }
          : {}),
    };
    config.interactions = {
      ...(form_config.chart.stack_by !== 'none'
        ? {
            tooltip: {
              trigger: 'item',
            },
          }
        : {}),
    };

    // Series config
    if (form_config.chart.layout_values && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(form_config.chart.type)) {
      config.series = {
        ...Object.values(form_config.chart.layout_values).reduce((acc, item) => {
          acc[item.value] = {
            name: item.legend || item.value,
            type: item.type,
            ...(form_config.chart.type !== 'horizontalBar_chart' ? { yAxisIndex: item.y_axis === 'primary' ? 0 : 1 } : {}),
            color: item.color,
            ...(!item.stack || form_config.chart.stack_by === 'none' ? { stack: false } : { stack: 'group1' }),
            lineColor: item.color,
            lineWidth: Number.parseInt(item.line_width),
            lineStyle: item.line_style,
            smooth: item.line_shape === 'curved',
            prefix: item.prefix || '',
            suffix: item.suffix || '',
          };
          return acc;
        }, {}),
      };
      const generateForecast = {
        ...Object.values(form_config.chart.layout_values).reduce((acc, item) => {
          if (!item.forecast_config)
            return acc;
          acc[item.value] = {
            name: item.forecast_config.forecast_series_name,
            series: item.forecast_config.forecast_for_series,
            from: item.forecast_config.starting_from,
            length: item.forecast_config.forecast_for,
            interval: item.forecast_config.bin_units,
            considerZero: item.forecast_config.consider_zeros,
            rate: {
              aggregation: item.forecast_config.rate.aggregation,
              ...(item.forecast_config.rate.rate_over === 'all'
                ? {}
                : {
                    direction: item.forecast_config.rate.rate_over,
                    duration: [item.forecast_config.rate.rate_over_number, item.forecast_config.bin_units],
                  }
              ),
            },
          };
          return acc;
        }, {}),
      };
      if (Object.keys(generateForecast).length) {
        config.data.generateForecast = Object.values(generateForecast);
      }
    }

    //  Display tab
    config.layout = {
      title: form_config.chart.title,
      subtitle: form_config.chart.subtitle,
    };
    config.legend = {
      show: form_config.chart.legend !== 'hide',
      position: form_config.chart.legend,
    };
    config.dataValues = {
      show: form_config.chart.values === 'show',
      compact: form_config.chart.compact,
      precision: form_config.chart.precision,
      prefix: form_config.chart.prefix || '',
      suffix: form_config.chart.suffix || '',
    };

    // Axes tab
    config.axes = {
      ...AXES_NAMES_SUPPORTED_CHARTS.includes(form_config.chart.type)
        ? {
            categoryName: form_config.chart.category_axis_name,
            valueName: form_config.chart.value_axis_name,
            ...(form_config.chart.secondary_y_axis ? { secondaryValueName: form_config.chart.secondary_y_axis } : {}),
          }
        : {},
      ...(SERIES_SUPPORTED_CHARTS.includes(form_config.chart.type)
        ? {
          // Custom ranges
            ...(!Number.isNaN(Number.parseInt(form_config.chart.custom_range_min)) ? { valueMin: Number.parseInt(form_config.chart.custom_range_min) } : {}),
            ...(!Number.isNaN(Number.parseInt(form_config.chart.custom_range_max)) ? { valueMax: Number.parseInt(form_config.chart.custom_range_max) } : {}),
            ...(DUAL_Y_AXIS_SUPPORTED_CHARTS.includes(form_config.chart.type)
              ? {
                  ...(!Number.isNaN(Number.parseInt(form_config.chart.secondary_value_min)) ? { secondaryValueMin: Number.parseInt(form_config.chart.secondary_value_min) } : {}),
                  ...(!Number.isNaN(Number.parseInt(form_config.chart.secondary_value_max)) ? { secondaryValueMax: Number.parseInt(form_config.chart.secondary_value_max) } : {}),
                }
              : {}),
            // Tick labels (Orientation)
            categoryLabels: form_config.chart.category_tick_label,
            valueLabels: form_config.chart.value_tick_label,
            secondaryValueLabels: form_config.chart.secondary_value_tick_label,
            // Scales (Linear, Log)
            valueScale: form_config.chart.primary_scale,
            secondaryValueScale: form_config.chart.secondary_scale,
          }
        : {}),
    };

    // Advanced tab
    if (
      ['column_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(form_config.chart.type)
      && form_config.chart.layout_category
      && chart_builder_columns_data_types.value[form_config.chart.layout_category] === 'date'
    ) {
      config.interactions = {
        ...config.interactions,
        dataZoom: {
          enabled: form_config.chart.timeseries,
          type: 'both',
        },
      };
    }

    if (form_config.chart.reference_lines) {
      config.reference_lines = form_config.chart.reference_lines.map(line => ({
        value: ['min', 'max', 'average', 'median', 'p25', 'p75', 'p90', 'p95'].includes(line.value) ? line.value : Number.parseInt(line.value),
        label: line.label,
        color: line.color,
        series: line.series,
        lineStyle: line.line_style,
      })).filter(line => line.value && line.series);
    }

    // Chart specific config
    if (form_config.chart.type === 'pareto_chart') {
      config.chartSpecific = {
        pareto: {
          show80PercentLine: form_config.chart.show_eighty_percent_line,
          eightyPercentLineColor: form_config.chart.eighty_percent_line_color,
          eightyPercentLineStyle: form_config.chart.eighty_percent_line_style,
          barColor: form_config.chart.bar_color,
          lineColor: form_config.chart.cumulative_line_color,
        },
      };
    }
    else if (form_config.chart.type === 'heatmap_chart') {
      config.chartSpecific = {
        heatmap: {
          colorScheme: BI_HEATMAP_PALETTES[form_config.chart.color_scheme]?.colors,
          colorType: form_config.chart.color_type,
          ...(form_config.chart.color_ranges?.length
            ? {
                colorPieces: form_config.chart.color_ranges.map(range => ({
                  label: range.label,
                  min: markers_value_options.value.includes(range.min) ? range.min : Number.parseInt(range.min),
                  max: markers_value_options.value.includes(range.max) ? range.max : Number.parseInt(range.max),
                  color: range.color,
                })),
              }
            : {}),
        },
      };
    }
    else if (form_config.chart.type === 'waterfall_chart') {
      config.chartSpecific = {
        waterfall: {
          showSum: form_config.chart.show_sum,
          positiveColor: form_config.chart.positive_color,
          negativeColor: form_config.chart.negative_color,
        },
      };
    }
    else if (form_config.chart.type === 'pyramid_chart') {
      config.chartSpecific = {
        pyramid: {
          labelsAtCenter: form_config.chart.show_labels_at_center,
          showPercentages: form_config.chart.show_percentages,
          is3D: form_config.chart.render_in_3d,
        },
      };
    }
    else if (form_config.chart.type === 'funnel_chart') {
      config.chartSpecific = {
        funnel: {
          labelsAtCenter: form_config.chart.show_labels_at_center,
          showPercentages: form_config.chart.show_percentages,
          is3D: form_config.chart.render_in_3d,
          percentOfPrevious: form_config.chart.compare_with_previous,
          maintainSlope: form_config.chart.maintain_slope,
        },
      };
    }
    else if (['gauge_chart', 'progress_chart'].includes(form_config.chart.type)) {
      config.chartSpecific = {
        [form_config.chart.type.replace('_chart', '')]: {
          color: form_config.chart.color || BI_DEFAULT_PALETTE_COLORS[0],
          min: !isNil(form_config.chart.minimum) ? Number(form_config.chart.minimum) : min_value_for_gauge_and_progress.value,
          max: !isNil(form_config.chart.maximum) ? Number(form_config.chart.maximum) : max_value_for_gauge_and_progress.value,
          ...(form_config.chart.markers
            ? {
                markers: form_config.chart.markers.map(marker => ({
                  label: marker.label,
                  value: markers_value_options.value.includes(marker.value) ? marker.value : Number.parseInt(marker.value),
                  color: marker.color,
                })),
              }
            : {}),
          ...(form_config.chart?.color_ranges?.length
            ? {
                colorRange: form_config.chart.color_ranges.map(range => ({
                  min: markers_value_options.value.includes(range.min) ? range.min : Number.parseInt(range.min),
                  max: markers_value_options.value.includes(range.max) ? range.max : Number.parseInt(range.max),
                  color: range.color,
                })),
              }
            : {}),
        },
      };
    }

    return config;
  }

  return {
    all_columns,
    stack_by_field_columns,
    markers_value_options,
    convertFormDataToConfig,
    min_value_for_gauge_and_progress,
    max_value_for_gauge_and_progress,
    date_bin_units,
    date_bin_label,
  };
}
