import { keyBy } from 'lodash-es';
import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBILocalStorage } from '~/bi/composables/useBILocalStorage.js';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();
const bi_local_storage = useBILocalStorage();

export const useBiStore = defineStore('bi', {
  state: () => ({
    dashboards: {},
    selected_dashboard: null,
    all_widget_details: {},
    all_schemas: [],
    all_schema_columns: {},
    selected_stage_index: null,
    is_chart_builder_form_valid: false,
    table_widget_config_change_detector: false,
    widget_data: [], // TODO: Rename to `widget_builder_data`
    table_metadata: {}, // TODO: Rename to `widget_builder_metadata`
    config_change_detection_counter: 1, // TODO: Rename to `widget_builder_config_change_detector`
    sql_query: '',
    fetching_widget_data: false,
    widget_builder_config: {
      query: {
        selected_database: 'grs_corvera',
        stages: [],
      },
      chart: {},
    },
    chart_builder_columns_data_types: {},
    modified_widgets: {
      created: {},
      updated: {},
    },
    current_grid_layout: [],
    is_fullscreen: false,
  }),
  getters: {
    all_schema_by_name() {
      return keyBy(this.all_schemas, 'name');
    },
    selected_dashboard_details() {
      return this.dashboards[this.selected_dashboard];
    },
  },
  actions: {
    set_is_fullscreen(value) {
      this.is_fullscreen = value;
    },
    initializeStore() {
      bi_local_storage.initializeStorage();
      this.dashboards = bi_local_storage.getDashboards();
      this.all_widget_details = bi_local_storage.getWidgets();
    },

    createDashboard(dashboard_data) {
      const new_dashboard = {
        uid: crypto.randomUUID(),
        name: dashboard_data.name,
        widgets: [],
        ...dashboard_data,
      };

      this.dashboards[new_dashboard.uid] = new_dashboard;
      bi_local_storage.createDashboard(new_dashboard);
      return new_dashboard;
    },
    updateDashboard(uid, updates) {
      if (this.dashboards[uid]) {
        this.dashboards[uid] = { ...this.dashboards[uid], ...updates };
        bi_local_storage.updateDashboard(uid, updates);
      }
    },
    deleteDashboard(uid) {
      if (this.dashboards[uid]) {
        delete this.dashboards[uid];
        bi_local_storage.deleteDashboard(uid);

        if (this.selected_dashboard === uid) {
          const remaining_dashboards = Object.keys(this.dashboards);
          this.selected_dashboard = remaining_dashboards.length > 0 ? remaining_dashboards[0] : null;
        }
      }
    },

    createWidget(widget_id, widget_data, save = false, layout_config = null) {
      if (save) {
        bi_local_storage.createWidget(widget_id, widget_data);
      }
      else {
        this.all_widget_details[widget_id] = widget_data;
        this.modified_widgets.created[widget_id] = widget_data;

        let current_layout = this.current_grid_layout;
        if (layout_config?.y) {
          current_layout = current_layout.map((widget) => {
            if (widget.y >= layout_config.y) {
              return { ...widget, y: widget.y + widget.h };
            }
            return widget;
          });
        }
        const x = 0;
        const y = current_layout.length > 0
          ? Math.max(...current_layout.map(w => w.y + w.h))
          : 0;
        const unique_i = String(Math.max(...current_layout.map(w => Number.parseInt(w.i) || 0)) + 1);
        const updated_layout = [...current_layout, {
          x: layout_config?.x || x,
          y: layout_config?.y || y,
          w: layout_config?.w || 5,
          h: layout_config?.h || 2,
          i: unique_i,
          widget_id,
        }];
        this.current_grid_layout = updated_layout;
      }
    },
    updateWidget(widget_id, updates, save = false) {
      if (save) {
        bi_local_storage.updateWidget(widget_id, updates);
      }
      else {
        if (this.all_widget_details[widget_id]) {
          this.all_widget_details[widget_id] = { ...this.all_widget_details[widget_id], ...updates };
          this.modified_widgets.updated[widget_id] = updates;
        }
      }
    },
    deleteWidget(widget_id) {
      this.current_grid_layout = this.current_grid_layout.filter(widget => widget.widget_id !== widget_id);
      delete this.all_widget_details[widget_id];
      delete this.modified_widgets.created[widget_id];
      delete this.modified_widgets.updated[widget_id];
    },

    set_chart_builder_columns_data_types() {
      const alias_to_column_mapping = this.alias_to_column_mapping();
      this.chart_builder_columns_data_types = Object.keys(alias_to_column_mapping).reduce((acc, key) => {
        let field_type = alias_to_column_mapping[key].field_type;
        switch (alias_to_column_mapping[key].field_type) {
          case 'string':
          case 'text':
          case 'text_array':
            field_type = 'text';
            break;
          case 'numeric':
          case 'integer':
          case 'decimal':
          case 'float':
            field_type = 'numeric';
            break;
          case 'date':
          case 'time':
          case 'timestamp':
            field_type = 'date';
            break;
          case 'boolean':
            field_type = 'boolean';
            break;
        }
        acc[key] = field_type;
        return acc;
      }, {});
    },
    alias_to_column_mapping() {
      if (!this.widget_builder_config.query.stages.length && !this.widget_builder_config.query.stages?.[0]?.columns?.length)
        return {};
      const column_mapping = {};
      this.widget_builder_config.query.stages.forEach(stage => stage.columns.forEach((column) => {
        column_mapping[column.alias] = column;
      }));
      return column_mapping;
    },
    async reset_widget_builder() {
      this.widget_builder_config = {
        query: {
          selected_database: 'grs_corvera',
          stages: [],
        },
        chart: {},
      };
      this.widget_data = [];
    },
    async getSchemas() {
      const response = await this.$services.bi_schema.table({
        id: this.widget_builder_config.query.selected_database,
      });
      this.all_schemas = response.data.tables.map(table => ({ label: table.name, name: table.name, columns: table.columns.map(column => ({ ...column, field: bi_query_builder.constructFieldName({ label: column.name, table_name: table.name }), label: column.name, alias: bi_query_builder.constructColumnAlias({ table_name: table.name, column_name: column.name }) })) }));
      return response.data;
    },
    async getTableColumns({ table_name, prefix_alias }) {
      if (this.all_schema_columns[table_name]) {
        return this.all_schema_columns[table_name];
      }
      const response = await this.$services.bi_schema.columns({
        id: this.widget_builder_config.query.selected_database,
        table_name,
      });
      let { columns } = response.data;
      const table_label = this.all_schema_by_name[table_name]?.label;
      columns = columns.map(column => ({ ...column, label: column.name, field: bi_query_builder.constructFieldName({ label: column.name, table_name }), alias: bi_query_builder.constructColumnAlias({ column_name: column.name, table_name: prefix_alias ? table_label : '' }) }));
      this.all_schema_columns[table_name] = columns;
    },
    async selectTable(table) {
      await this.getTableColumns({ table_name: table.name });
    },
    async getWidgetData(selected_database, stages) {
      try {
        this.fetching_widget_data = true;
        const response = await this.$services.bi_query.execute({
          id: selected_database,
          body: bi_query_builder.getStageConfig(stages),
        });
        return response.data;
      }
      finally {
        this.fetching_widget_data = false;
      }
    },
    async getColumnValues({ table_name, column_name, query }) {
      const response = await this.$services.bi_schema.column_values({
        id: this.widget_builder_config.query.selected_database,
        table_name,
        column_name,
        query,
      });
      return response.data;
    },
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
