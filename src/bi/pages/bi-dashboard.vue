<script setup>
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted } from 'vue';
import { useModal } from 'vue-final-modal';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import BiCreateDashboardModal from '~/bi/components/bi-create-dashboard-modal.vue';
import BiCreateRichTextWidget from '~/bi/components/bi-create-rich-text-widget.vue';
import BiCreateWidget from '~/bi/components/bi-create-widget.vue';
import { useBiExport } from '~/bi/composables/bi-export.composable';
import { useBILocalStorage } from '~/bi/composables/useBILocalStorage';
import { useBiStore } from '~/bi/store/bi.store';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import { useCommonStore } from '~/common/stores/common.store';
import { sleep } from '~/common/utils/common.utils';
import DashboardExitConfirmationPopup from '~/dashboard/components/dashboard-exit-confirmation-popup.vue';

const $t = inject('$t');

const route = useRoute();
const router = useRouter();

const bi_store = useBiStore();
const common_store = useCommonStore();
const { dashboards, selected_dashboard, selected_dashboard_details, modified_widgets, current_grid_layout, is_fullscreen } = storeToRefs(bi_store);
const { active_asset } = storeToRefs(common_store);

const { clearAllData } = useBILocalStorage();
const { exportDashboard } = useBiExport();

const state = reactive({
  is_loading: false,
  is_editing: false,
  show_sidebar: false,
  is_exporting: false,
  is_saving: false,
});

let export_abort_controller;

const create_widget = useModal({
  component: BiCreateWidget,
  attrs: {
    onClose() {
      create_widget.close();
    },
  },
});

const create_rich_text_widget = useModal({
  component: BiCreateRichTextWidget,
  attrs: {
    onClose() {
      create_rich_text_widget.close();
    },
  },
});

const create_dashboard = useModal({
  component: BiCreateDashboardModal,
  attrs: {
    onClose() {
      create_dashboard.close();
    },
    async onSave(name) {
      // TODO: API call here for creating the dashboard
      await sleep(1000);
      const new_dashboard = bi_store.createDashboard({ name });
      selected_dashboard.value = new_dashboard.uid;
      create_dashboard.close();
    },
  },
});

const delete_dashboard = useModal({
  component: HawkDeletePopup,
  attrs: {
    onClose() {
      delete_dashboard.close();
    },
  },
});

const dashboard_exit_confirmation_modal = useModal({
  component: DashboardExitConfirmationPopup,
});

const create_widget_menu_items = computed(() => {
  return [
    { label: 'Widget', on_click: () => createWidget() },
    { label: $t('Rich text'), on_click: () => createRichTextWidget() },
  ];
});

const dashboard_action_items = computed(() => {
  return [
    { label: $t('Edit'), on_click: () => (state.is_editing = true) },
    { label: $t('Export PDF'), on_click: () => setExportingState(true) },
    { label: $t('Archive'), on_click: () => {} },
    ...(Object.keys(dashboards.value).length > 1 ? [{ label: $t('Delete'), on_click: () => deleteDashboard() }] : []),
  ];
});

const bi_grid = ref(null);

function createDashboard() {
  create_dashboard.open();
}

function onSelectDashboard(dashboard) {
  if (state.is_editing) {
    dashboard_exit_confirmation_modal.patchOptions({
      attrs: {
        onClose() {
          dashboard_exit_confirmation_modal.close();
        },
        onConfirm: async () => {
          state.is_editing = false;
          selected_dashboard.value = dashboard.uid;
          dashboard_exit_confirmation_modal.close();
        },
      },
    });
    dashboard_exit_confirmation_modal.open();
  }
  else {
    selected_dashboard.value = dashboard.uid;
  }
}

function createWidget() {
  create_widget.patchOptions({
    attrs: {
      mode: 'create',
      view: 'data-builder',
      widgetData: [],
      config: {},
      onClose() {
        create_widget.close();
      },
    },
  });
  create_widget.open();
}

function createRichTextWidget() {
  create_rich_text_widget.open();
}

async function onSaveDashboard() {
  state.is_saving = true;

  // Merge the current grid layout with widget details from modified widgets
  const updated_widgets = current_grid_layout.value.map(layout_widget => {
    const widget_id = layout_widget.widget_id;

    // Check if this widget was created or updated
    const created_widget = modified_widgets.value.created[widget_id];
    const updated_widget = modified_widgets.value.updated[widget_id];

    if (created_widget) {
      // New widget - merge layout with widget details
      return {
        ...layout_widget,
        name: created_widget.name,
        config: created_widget.config,
      };
    } else if (updated_widget) {
      // Updated widget - merge existing widget with updates
      const existing_widget = bi_store.selected_dashboard_widgets.find(w => w.widget_id === widget_id);
      return {
        ...existing_widget,
        ...layout_widget,
        ...updated_widget,
      };
    } else {
      // Existing widget - just update layout
      const existing_widget = bi_store.selected_dashboard_widgets.find(w => w.widget_id === widget_id);
      return {
        ...existing_widget,
        ...layout_widget,
      };
    }
  });

  // Update the dashboard with the merged widgets
  bi_store.updateDashboard(selected_dashboard.value, {
    widgets: updated_widgets,
  });

  // Clear modified widgets tracking
  modified_widgets.value.created = {};
  modified_widgets.value.updated = {};

  // TODO: API call here for saving the dashboard
  await sleep(1000);
  await nextTick();
  state.is_editing = false;
  state.is_saving = false;
}

function setExportingState(value) {
  state.is_exporting = value;
}

function cancelExport() {
  export_abort_controller.abort();
  setExportingState(false);
}

async function exportCurrentDashboard() {
  export_abort_controller = new AbortController();
  await exportDashboard(current_grid_layout.value, bi_grid.value, export_abort_controller);
  export_abort_controller = null;
}

function deleteDashboard() {
  delete_dashboard.patchOptions({
    attrs: {
      header: `Delete dashboard: "${selected_dashboard_details.value.name}"`,
      content: 'Are you sure you want to delete this dashboard?',
      confirm: async () => {
        // TODO: API call here for deleting the dashboard
        await sleep(1000);
        bi_store.deleteDashboard(selected_dashboard.value);
        delete_dashboard.close();
      },
    },
  });
  delete_dashboard.open();
}

function leaveDashboard(next) {
  dashboard_exit_confirmation_modal.patchOptions({
    attrs: {
      onClose() {
        window.history.forward();
        dashboard_exit_confirmation_modal.close();
        return false;
      },
      onConfirm: async () => {
        state.is_editing = false;
        dashboard_exit_confirmation_modal.close();
        next();
      },
    },
  });
  dashboard_exit_confirmation_modal.open();
}

function updateRouterQuery() {
  router.replace({
    ...route,
    query: {
      ...route.query,
      dashboard: selected_dashboard.value,
    },
  });
}

function onFullscreenClicked() {
  if (is_fullscreen.value) {
    document.exitFullscreen();
  }
  else {
    const element = document.getElementById('bi-fullscreen-container');
    if (element.requestFullscreen)
      element.requestFullscreen();

    else if (element.mozRequestFullScreen)
      element.mozRequestFullScreen();

    else if (element.webkitRequestFullscreen)
      element.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);

    else if (element.msRequestFullscreen)
      element.msRequestFullscreen();
  }
}

function onFullscreenToggled() {
  bi_store.set_is_fullscreen(!!document.fullscreenElement);
}

watch(() => selected_dashboard_details.value, (new_val) => {
  if (new_val?.uid) {
    // NOTE: This cloneDeep here and the one in onMounted are added temporarily so that the reference is not carried forward (for safety). This can be removed later on based on the final requirements of how switching b/w dashboards should work.
    current_grid_layout.value = cloneDeep(dashboards.value[new_val.uid].widgets);
    updateRouterQuery();
  }
}, { deep: true });

onBeforeRouteLeave((_to, _from, next) => {
  if (state.is_editing)
    leaveDashboard(next);
  else next();
});

onMounted(async () => {
  state.is_loading = true;
  // TODO: API call here for getting the dashboards
  await sleep(1000);

  bi_store.initializeStore();

  if (route.query.dashboard && dashboards.value[route.query.dashboard]) {
    selected_dashboard.value = dashboards.value[route.query.dashboard].uid;
  }
  else {
    selected_dashboard.value = Object.keys(dashboards.value)[0];
    updateRouterQuery();
  }

  if (selected_dashboard.value && selected_dashboard_details.value) {
    current_grid_layout.value = cloneDeep(selected_dashboard_details.value.widgets);
  }

  state.is_loading = false;

  document.addEventListener('fullscreenchange', onFullscreenToggled);
});

onBeforeUnmount(() => {
  document.removeEventListener('fullscreenchange', onFullscreenToggled);
});
</script>

<template>
  <div>
    <HawkLoader v-if="state.is_loading" />
    <div v-else class="flex w-full items-start">
      <DashboardSidebar :show_sidebar="state.show_sidebar" class="top-[65px]" />
      <div class="w-full">
        <div class="sticky top-[65px] z-10 bg-white">
          <div class="px-4 pt-3 flex items-center gap-3">
            <HawkButton
              v-if="active_asset"
              icon
              type="text"
              class="-mt-3 -ml-2 mr-2"
              @click="state.show_sidebar = !state.show_sidebar"
            >
              <IconHawkMenuThree />
            </HawkButton>
            <HawkTabs
              :tabs="Object.values(dashboards).map(item => ({ uid: item.uid, label: item.name }))"
              :active_item="selected_dashboard"
              container_class="border-transparent"
              @tab-click="onSelectDashboard"
            />
            <HawkButton
              icon
              type="text"
              class="-mt-3"
              @click="createDashboard"
            >
              <IconHawkPlus />
            </HawkButton>
            <HawkButton
              class="-mt-3 ml-2"
              @click="clearAllData"
            >
              Clear localStorage and reload
            </HawkButton>
          </div>
          <hr>
        </div>
        <div id="bi-fullscreen-container" :class="is_fullscreen ? 'overflow-auto' : ''">
          <div class="sticky z-10 bg-white" :class="is_fullscreen ? 'top-[0px]' : 'top-[114px]'">
            <div class="px-4 py-2 bg-gray-50 flex justify-between items-center">
              <div class="flex items-center">
                Filters (Coming soon™)
                <HawkButton v-if="state.is_editing" icon type="text" class="hover:!bg-gray-200">
                  <IconHawkSettingsOne />
                </HawkButton>
              </div>
              <div class="flex items-center">
                <HawkButton
                  v-if="!state.is_editing"
                  v-tippy="{
                    content: $t('Fullscreen mode'),
                    placement: 'bottom',
                  }"
                  icon
                  type="text"
                  @click="onFullscreenClicked"
                >
                  <IconHawkFullScreen class="w-4 h-4 text-gray-600" />
                </HawkButton>
                <template v-if="!is_fullscreen">
                  <div v-if="state.is_editing" class="flex items-center gap-3">
                    <HawkMenu :items="create_widget_menu_items" position="fixed" additional_trigger_classes="!ring-0">
                      <template #trigger="{ open }">
                        <HawkButton type="outlined">
                          Add
                          <IconHawkChevronDown v-if="!open" />
                          <IconHawkChevronUp v-else />
                        </HawkButton>
                      </template>
                    </HawkMenu>
                    <HawkButton :loading="state.is_saving" @click="onSaveDashboard">
                      <IconHawkRocketTwo />
                      Publish
                    </HawkButton>
                  </div>
                  <div v-else class="flex items-center gap-3">
                    <HawkMenu :items="dashboard_action_items">
                      <template #trigger>
                        <HawkButton icon type="text" class="hover:!bg-gray-200">
                          <IconHawkSettingsOne />
                        </HawkButton>
                      </template>
                    </HawkMenu>
                  </div>
                </template>
              </div>
            </div>
            <hr>
          </div>
          <BiGrid
            ref="bi_grid"
            :is-editing="state.is_editing"
          />
        </div>
      </div>
      <HawkExportToast
        v-if="state.is_exporting"
        :submit="exportCurrentDashboard"
        :progress_text="$t('Exporting PDF')"
        :completed_text="$t('Exported PDF')"
        @cancel="cancelExport"
        @close="setExportingState(false)"
      />
    </div>
  </div>
</template>
