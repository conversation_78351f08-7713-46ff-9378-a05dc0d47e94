<script setup>
import { GridLayout } from 'grid-layout-plus';
import { storeToRefs } from 'pinia';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  isEditing: {
    type: Boolean,
    default: false,
  },
});

const bi_store = useBiStore();
const { all_widget_details, current_grid_layout } = storeToRefs(bi_store);
</script>

<template>
  <GridLayout
    v-model:layout="current_grid_layout"
    :row-height="30"
    :class="props.isEditing ? 'active-grid' : ''"
    :is-draggable="props.isEditing"
    :is-resizable="props.isEditing"
  >
    <template #item="{ item }">
      <BiWidget
        :is-editing="props.isEditing"
        :widget-id="item.widget_id"
        :widget-name="all_widget_details[item.widget_id].name"
        :config="all_widget_details[item.widget_id].config"
        :layout-config="item"
        :class="props.isEditing ? 'select-none' : ''"
      />
    </template>
  </GridLayout>
</template>

<style scoped>
.vgl-layout {
  @apply bg-gray-50;
}

.vgl-layout::before {
  position: absolute;
  width: calc(100% - 5px);
  height: calc(100% - 5px);
  margin: 5px;
  content: '';
  background-repeat: repeat;
  background-size: calc(calc(100% - 5px) / 12) 40px;
}

:deep(.vgl-item:not(.vgl-item--placeholder)) {
  @apply relative;
}

.active-grid :deep(.vgl-item:not(.vgl-item--placeholder)):hover {
  @apply border-transparent;
}

.active-grid :deep(.vgl-item:not(.vgl-item--placeholder)):hover::after {
  content: '';
  @apply absolute inset-[-2px] border-2 border-dashed border-primary-600 rounded pointer-events-none;
}

:deep(.vgl-item__resizer) {
  visibility: hidden;
}
:deep(.vgl-item:hover .vgl-item__resizer) {
  visibility: visible;
}

:deep(.vgl-item__resizer::before) {
  border: 0 solid #1570ef;
  border-right-width: 4px;
  border-bottom-width: 4px;
  right: -5px;
  bottom: -5px;
  @apply rounded-br;
}

:deep(.vgl-item__resizer) {
  @apply right-[2px] bottom-[2px] w-3 h-3;
}

:deep(.vgl-item--resizing) {
  opacity: 90%;
}

:deep(.vgl-item--static) {
  background-color: #cce;
}

.text {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  margin: auto;
  font-size: 24px;
  text-align: center;
}

.active-grid {
  background-size: 22.18px 17.7px;
  background-image: conic-gradient(transparent 0deg, transparent 90deg, white 90deg, white 180deg, transparent 180deg, transparent 270deg, white 270deg);
  background-clip: padding-box;
  min-height: 25rem;
}
</style>
