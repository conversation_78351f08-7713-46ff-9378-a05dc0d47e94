<script setup>
import { storeToRefs } from 'pinia';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import BiQueryBuilderColumnsHierarchy from './bi-query-builder-columns-hierarchy.vue';

const props = defineProps({
  // New API: multiple stages, each stage has { label, columns, tables }
  stages: {
    type: Array,
    default: () => ([]),
  },
  // Backwards-compatibility: if caller still passes `tables`, we accept it and treat as single unnamed stage
  tables: {
    type: Array,
    default: () => ([]),
  },
  stageIndex: {
    type: Number,
  },
  isDropdown: {
    type: Boolean,
    default: true,
  },
  hasFunctions: {
    type: Boolean,
    default: true,
  },
  isFilters: {
    type: Boolean,
    default: false,
  },
  showStageHeader: {
    type: Boolean,
    default: false,
  },
  slotContainerClasses: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['selected', 'expression']);
const { $t } = useCommonImports();
const { getIconsForType, getOperatorsForType, constructColumnAlias, buildHierarchy, constructFieldName } = useBIQueryBuilder();
const bi_store = useBiStore();
const { widget_builder_config } = storeToRefs(bi_store);
const { stages } = toRefs(widget_builder_config.value.query);

const date_bins = [{ label: $t('Week'), value: 'week' }, { label: $t('Month'), value: 'month' }, { label: $t('Quarter'), value: 'quarter' }, { label: $t('Year'), value: 'year' }];

const dropdown = ref(null);
const dropdown_content = ref(null);
const sub_menu = ref(null);
const column_search_input_ref = ref(null);
const agg_search_input_ref = ref(null);
const search_input = ref('');
const search_operator_input = ref('');
const hovered_column = ref(null);
const hovered_table = ref(null);
const form$ = ref(null);
const hovered_column_operators = computed(() => props.hasFunctions ? getOperatorsForType(hovered_column.value?.type) : []);
const hovered_column_operators_with_search = computed(() => hovered_column_operators.value.filter(operator => operator.label.toLowerCase().includes(search_operator_input.value.toLowerCase())));
const showLeafMenu = computed(() => {
  return props.isFilters ? hovered_column.value : hovered_column_operators.value?.length;
});

const showStageHeader = computed(() => props.showStageHeader && bi_store.widget_builder_config.query.stages?.length > 1);

// Collapsible state for tables in the dropdown
const expanded_tables = ref({});
// Collapsible state for stages
const expanded_stages = ref({});

function stageKey(stage, sidx) {
  return stage?.uid ?? `${stage?.label ?? 'stage'}_${sidx}`;
}

function tableKey(table, tidx, stage) {
  // include stage label in key to avoid collisions between tables in different stages
  const stagePart = stage?.label ? `${stage.label}__` : '';
  return table?.uid ?? `${stagePart}${table?.label ?? 'table'}_${tidx}`;
}

function isTableOpen(table, tidx, stage) {
  return !!expanded_tables.value[tableKey(table, tidx, stage)];
}

function toggleTable(table, tidx, stage) {
  const key = tableKey(table, tidx, stage);
  expanded_tables.value[key] = !expanded_tables.value[key];
}

function isStageOpen(stage, sidx) {
  return !!expanded_stages.value[stageKey(stage, sidx)];
}

function toggleStage(stage, sidx) {
  const key = stageKey(stage, sidx);
  expanded_stages.value[key] = !expanded_stages.value[key];
}

// Helper to filter hierarchy nodes (used for stage-level columns)
function filterNodes(nodes, search) {
  if (!search)
    return nodes || [];
  const q = search.toLowerCase();
  function filterNode(node) {
    if (node.is_hierarchy && Array.isArray(node.children)) {
      const filteredChildren = node.children.map(filterNode).filter(Boolean);
      if (filteredChildren.length) {
        return { ...node, children: filteredChildren };
      }
      if (node.label && node.label.toLowerCase().includes(q)) {
        return { ...node };
      }
      return null;
    }
    if (node.label && node.label.toLowerCase().includes(q)) {
      return node;
    }
    return null;
  }
  return (nodes || []).map(n => filterNode(n)).filter(Boolean);
}

// Compute filtered stages (each stage contains filtered tables and stage-level columns)
const filtered_stages = computed(() => {
  // derive input stages: prefer stages prop, fallback to single stage from tables prop for backwards compatibility
  const inputStages = (props.stages && props.stages.length) ? props.stages : [{ label: null, tables: props.tables || [], columns: [] }];
  return inputStages.map((stage) => {
    const tables = filterColumns(stage.tables || [], search_input.value || '');
    const columns = filterNodes(stage.columns || [], search_input.value || '');
    return { ...stage, tables, columns };
  });
});

watch(
  () => filtered_stages.value,
  (newStages) => {
    const tableMap = {};
    const stageMap = {};
    newStages.forEach((stage, sIdx) => {
      stageMap[stageKey(stage, sIdx)] = true;
      (stage.tables || []).forEach((table, tIdx) => {
        tableMap[tableKey(table, tIdx, stage)] = true;
      });
    });
    expanded_tables.value = { ...tableMap, ...expanded_tables.value };
    expanded_stages.value = { ...stageMap, ...expanded_stages.value };
  },
  { immediate: true },
);

// Filter the columns based on the search input and return the list of new tables that have columns that match the search input
// It should return only the columns that are matching the search input
function filterColumns(tables, search_input) {
  // If search is empty, return original tables
  if (!search_input)
    return tables;

  const q = search_input.toLowerCase();

  // Recursively filter a node (leaf or hierarchy). Returns a new node or null.
  function filterNode(node) {
    // hierarchy node: try to filter children recursively
    if (node.is_hierarchy && Array.isArray(node.children)) {
      const filteredChildren = node.children.map(filterNode).filter(Boolean);
      // If any child matches, keep node with those children
      if (filteredChildren.length) {
        return { ...node, children: filteredChildren };
      }
      // If the hierarchy node's label itself matches, keep the whole subtree
      if (node.label && node.label.toLowerCase().includes(q)) {
        return { ...node };
      }
      // otherwise exclude
      return null;
    }

    // leaf node: include if label matches
    if (node.label && node.label.toLowerCase().includes(q)) {
      return node;
    }
    return null;
  }

  const new_tables = tables.map((table) => {
    const columns = table.columns.map(col => filterNode(col)).filter(Boolean);
    return { ...table, columns };
  });

  const filtered_tables = new_tables.filter(table => table.columns.length > 0);

  // If nothing matched, return original tables (preserve existing UI)
  return filtered_tables.length ? filtered_tables : tables;
}

function onColumnChevronClick(e, column, table) {
  if (agg_search_input_ref?.value) {
    setTimeout(() => {
      agg_search_input_ref.value?.focus();
    }, 0);
  }

  hovered_column.value = column;
  hovered_table.value = table;
  search_operator_input.value = '';
  // Set the sub menu position of the top based on the mouse position and the dropdown location in the screen
  const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
  const rect = e.srcElement.getBoundingClientRect();

  // This ensures popup is always positioned at the end of dropdown content regardless of what was clicked
  sub_menu.value.style.left = `${dropdownContentRect.left + dropdownContentRect.width}px`;
  if ((e.clientY + dropdownContentRect.height) > window.innerHeight) {
    sub_menu.value.style.bottom = `${window.innerHeight - rect.bottom + 5}px`;
    sub_menu.value.style.top = 'auto';
  }
  // If the position is at the top of the screen, set the top position to the top of the dropdown
  else {
    sub_menu.value.style.top = `${rect.top - 50}px`;
    sub_menu.value.style.bottom = 'auto';
  }
}

function getTableName() {
  return hovered_table.value.name === stages.value[0].table ? null : hovered_table.value.name;
}

function getFieldName(column_name, table_name) {
  return constructFieldName({ label: column_name, table_name: table_name || 'results' });
}

function onOperatorClicked(operator) {
  if (hovered_column.value.type === 'array')
    emit('selected', { label: hovered_column.value.label, field_type: operator.output_type, unnest: true, field: getFieldName(hovered_column.value.label, hovered_table.value.name), table_name: getTableName(), alias: constructColumnAlias({ table_name: getTableName(), column_name: `${hovered_column.value.label}☰` }) });
  else
    emit('selected', { label: hovered_column.value.label, field_type: operator.output_type, agg: operator.name, is_aggregation: true, field: getFieldName(hovered_column.value.label, hovered_table.value.name), table_name: getTableName(), alias: constructColumnAlias({ table_name: getTableName(), column_name: hovered_column.value.label, agg: operator.label }) });
  hovered_column.value = null;
}

function onColumnClicked(column, table) {
  emit('selected', { label: column.label, field_type: column.type, table_name: table.name, field: getFieldName(column.label, table.name), alias: column.alias, is_table_column: true });
}

function onExpressionClicked() {
  hovered_column.value = null;
  emit('expression');
}

function onDateBinClicked(bin, bin_label) {
  emit('selected', { label: hovered_column.value.label, field_type: hovered_column.value.type, [hovered_column.value.type === 'date' ? 'dateBin' : 'binWidth']: bin, is_bin: true, field: getFieldName(hovered_column.value.label, hovered_table.value.name), table_name: getTableName(), alias: constructColumnAlias({ table_name: getTableName(), column_name: hovered_column.value.label, bin: bin_label }) });
  hovered_column.value = null;
}

function onBinApply() {
  const bin = form$.value.data?.date_bin_type || form$.value.data?.bin_width;
  let bin_label = '';

  if (form$.value.data?.date_bin_type) {
    bin_label = date_bins.find(bin => bin.value === form$.value.data.date_bin_type)?.label;
  }
  else if (form$.value.data?.bin_width) {
    bin_label = form$.value.data.bin_width.toString();
  }

  onDateBinClicked(bin, bin_label);
}
function onBinCancel() {
  hovered_column.value = null;
};

function onColTypeChange(new_val) {
  if (new_val === 'bins' && hovered_column.value?.type === 'integer') {
    nextTick(() => {
      form$.value.elements$?.bin_width?.focus();
    });
  }
  if (new_val === 'aggregations') {
    nextTick(() => {
      agg_search_input_ref.value?.focus();
    });
  }
}

// Add a function to reset the hovered column
function resetHoveredColumn() {
  hovered_column.value = null;
}

// Expose the function so parent components can call it
defineExpose({
  resetHoveredColumn,
});

onMounted(() => {
  nextTick(() => {
    if (column_search_input_ref.value) {
      column_search_input_ref.value.focus();
    }
  });
});
</script>

<template>
  <div
    ref="dropdown" class="bi-columns-dropdown z-20" @keydown.enter.prevent.stop
    @keydown.space.prevent.stop
  >
    <div ref="dropdown_content" class=" rounded-lg  w-72 flex flex-col bg-white">
      <div class="relative">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <IconHawkSearch class="text-gray-600 size-4" />
        </div>
        <input
          ref="column_search_input_ref"
          v-model="search_input"
          name="input"
          autocomplete="off"
          :placeholder="$t('Search for a field')"
          class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 focus:outline-none text-sm sm:leading-6 rounded-lg"
        >
      </div>
      <div class="bi-columns-dropdown-content min-h-[300px] max-h-80 overflow-auto scrollbar border-t">
        <div v-for="(stage, sIndex) in filtered_stages" :key="`${stage.label || 'stage'}_${sIndex}`">
          <div v-show="showStageHeader" class="text-sm text-gray-700 font-semibold flex items-center gap-1 px-3 py-2 cursor-pointer select-none" @click="toggleStage(stage, sIndex)">
            <IconHawkPackage class="size-4" />
            <span class="ml-2 flex-1">{{ stage.label || `${$t('Stage')} ${stageIndex}` }}</span>
            <IconHawkChevronRight class="size-4 transition-transform" :class="{ 'rotate-90': isStageOpen(stage, sIndex) }" />
          </div>
          <div v-show="isStageOpen(stage, sIndex)">
            <!-- Stage-level columns (if any) -->
            <div v-if="stage.columns && stage.columns.length" :class="showStageHeader ? 'pl-3' : ''">
              <BiQueryBuilderColumnsHierarchy
                v-for="column in (buildHierarchy(stage.columns))"
                :key="`stage_${sIndex}_column_${column.label}`"
                :node="column"
                :is-stage-columns="true"
                :table="{ label: stage.label }"
                :hovered-column="hovered_column"
                :has-functions="hasFunctions"
                @chevron-click="payload => onColumnChevronClick(payload.e, payload.column, payload.table)"
                @select="payload => isFilters ? onColumnChevronClick(payload.e, payload.column, payload.table) : onColumnClicked(payload.column, payload.table)"
              />
            </div>
            <div v-for="(table, tIndex) in (stage.tables || [])" :key="`${stage.label || 'stage'}_${table.label}_${tIndex}`">
              <div class="text-sm text-gray-700 font-semibold flex items-center gap-1 py-2 pr-3 cursor-pointer select-none" :class="showStageHeader ? 'pl-6' : 'pl-3'" @click="toggleTable(table, tIndex, stage)">
                <IconHawkDatabaseTwo v-if="table.label" class="size-4" />
                <IconHawkPackage v-else class="size-4" />

                <span class="ml-2 flex-1">{{ table.label || `${$t('Stage')} ${stageIndex}` }}</span>
                <IconHawkChevronRight class="size-4 transition-transform" :class="{ 'rotate-90': isTableOpen(table, tIndex, stage) }" />
              </div>
              <div v-show="isTableOpen(table, tIndex, stage)" :class="showStageHeader ? 'pl-3' : ''">
                <BiQueryBuilderColumnsHierarchy
                  v-for="column in (buildHierarchy(table.columns))"
                  :key="column.label"
                  :node="column"
                  :table="table"
                  :hovered-column="hovered_column"
                  :has-functions="hasFunctions"
                  @chevron-click="payload => onColumnChevronClick(payload.e, payload.column, payload.table)"
                  @select="payload => isFilters ? onColumnChevronClick(payload.e, payload.column, payload.table) : onColumnClicked(payload.column, payload.table)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="hasFunctions" class="border-t">
        <span type="text" class="text-sm font-medium flex py-2 px-1 text-primary-700 hover:bg-gray-50 cursor-pointer rounded-lg items-center" @click="onExpressionClicked">
          <component :is="getIconsForType('formula')" class=" size-4 mx-2" />
          {{ $t('Custom Expression') }}
        </span>
      </div>
    </div>
    <div v-show="showLeafMenu" ref="sub_menu" class="dropdown-sub-menu shadow-lg border border-gray-200 rounded-lg fixed w-[300px] max-h-[400px] left-[340px] -mb-8 bg-white z-20" :class="[slotContainerClasses]">
      <slot :column="hovered_column" :table="hovered_table">
        <div class="p-2">
          <template v-if="hovered_column?.type === 'date' || hovered_column?.type === 'integer'">
            <Vueform ref="form$" size="sm" :display-errors="false">
              <div class="flex col-span-12 items-center">
                <RadiogroupElement
                  class="px-2"
                  name="custom_column_type"
                  :items="[{ label: 'Aggregations', value: 'aggregations' }, { label: 'Bins', value: 'bins' }]"
                  default="aggregations"
                  :add-classes="{
                    RadiogroupElement: {
                      wrapper: ['!flex-row', 'gap-6'],
                    },
                  }"
                  @change="onColTypeChange"
                />
                <IconHawkInfoCircle v-tippy="{ content: 'Group date by week, month, etc to summarize the data' }" class="w-4 h-4 mb-1" />
              </div>
              <!-- Bins -->
              <template v-if="form$?.data?.custom_column_type === 'bins'">
                <!-- Date bins -->
                <SelectElement
                  v-if="hovered_column?.type === 'date'"
                  :items="date_bins"
                  :can-clear="false"
                  name="date_bin_type"
                  default="week"
                  class="capitalize w-full px-2"
                  :native="false"
                />
                <!-- Bin count -->
                <div v-if="hovered_column?.type === 'integer'" class="col-span-12 px-2">
                  <p class="text-sm col-span-12 font-medium mb-2">
                    Bin width
                  </p>
                  <TextElement
                    name="bin_width"
                    :placeholder=" $t('Enter value')"
                    input-type="number"
                    autocomplete="off"
                  />
                </div>
                <div class="col-span-12 px-2">
                  <hr class="my-2">
                  <div class="mt-2 flex items-center justify-end text-sm gap-2 font-semibold">
                    <div class="flex items-center">
                      <div class="text-gray-600 cursor-pointer p-2" @click="onBinCancel">
                        {{ $t('Cancel') }}
                      </div>
                    </div>
                    <div class="text-primary-700 cursor-pointer" @click="onBinApply">
                      {{ $t('Apply') }}
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <!-- Aggregations -->
                <div class="relative col-span-12 border-t border-b">
                  <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <IconHawkSearch class="text-gray-600 size-4" />
                  </div>
                  <input
                    ref="agg_search_input_ref"
                    v-model="search_operator_input"
                    name="input"
                    autocomplete="off"
                    :placeholder="$t('Search options')"
                    class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 focus:outline-none text-sm sm:leading-6 rounded-lg"
                  >
                </div>
                <div class="col-span-12 overflow-auto scrollbar max-h-[280px]">
                  <div v-if="!hovered_column_operators_with_search.length">
                    <div class=" gap-2 px-2 py-2 rounded-lg relative text-gray-400 text-sm font-medium flex items-center text-center">
                      {{ $t('No results found') }}
                    </div>
                  </div>
                  <div v-for="operator in hovered_column_operators_with_search" :key="operator.label" class="flex items-center gap-2 px-2 py-2 cursor-pointer hover:bg-gray-50 rounded-lg relative" @click="onOperatorClicked(operator)">
                    <span class=" text-gray-700 text-sm font-medium">{{ operator.label }}</span>
                  </div>
                </div>
              </template>
            </Vueform>
          </template>
          <template v-else>
            <span v-if="hovered_column?.type !== 'array'" class="text-sm text-gray-900 font-medium px-2">
              {{ $t('Aggregations') }}
            </span>
            <span v-else class="text-sm text-gray-900 font-medium px-2">
              {{ $t('Transformations') }}
            </span>
            <div class="relative col-span-12 border-t border-b mt-2">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <IconHawkSearch class="text-gray-600 size-4" />
              </div>
              <input
                ref="agg_search_input_ref"
                v-model="search_operator_input"
                name="input"
                autocomplete="off"
                :placeholder="$t('Search options')"
                class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 focus:outline-none text-sm sm:leading-6 rounded-lg"
              >
            </div>
            <div class="col-span-12 overflow-auto scrollbar max-h-[280px]">
              <div v-if="!hovered_column_operators_with_search.length">
                <div class=" gap-2 px-2 py-2 rounded-lg relative text-gray-400 text-sm font-medium flex items-center text-center">
                  {{ $t('No results found') }}
                </div>
              </div>
              <div v-for="operator in hovered_column_operators_with_search" :key="operator.label" class="flex items-center gap-2 px-2 py-2 cursor-pointer hover:bg-gray-50 rounded-lg relative" @click="onOperatorClicked(operator)">
                <span class=" text-gray-700 text-sm font-medium">{{ operator.label }}</span>
              </div>
            </div>
          </template>
        </div>
      </slot>
    </div>
  </div>
</template>
