<script setup>
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
  fields: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['selected', 'expression']);
const { $t } = useCommonImports();

function onColumnsSelected(field) {
  emit('selected', field);
}

function onExpressionClicked(field) {
  emit('expression', field);
}

const getLabel = field => field.alias;
function construct_table_object(table) {
  return {
    label: table.label,
    columns: [...table.columns.map(field => ({ label: getLabel(field), type: field.type, field: field.alias, alias: field.alias }))],
  };
}
const tables_with_fields = computed(() => {
  const tables = [];
  if (props.fields.length > 0)
    tables.push(construct_table_object({ label: `Stage ${props.stageIndex + 1}`, columns: props.fields, tables: [] }));
  if (props.stageIndex === 0) {
    return tables;
  }
  else {
    const previous_stage_tables = props.tables[0];
    const previous_stage_columns = [previous_stage_tables].flatMap(table => table.columns.map(column => ({ ...column, field: column.alias, table_name: table.name })));
    if (previous_stage_columns.length)
      tables.push(construct_table_object({ label: `Stage ${props.stageIndex}`, columns: previous_stage_columns, tables: [] }));
    return tables;
  }
});

const dropdown = ref(null);
function updateDropdownPosition() {
  if (dropdown.value) {
    nextTick(() => {
      dropdown.value.setFixedPosition();
    });
  }
}
</script>

<template>
  <hawk-menu ref="dropdown" position="fixed" additional_trigger_classes="!ring-0 !focus:ring-0 !px-0 !py-0" additional_dropdown_classes="transition duration-150 ease-in-out">
    <template #trigger="{ open }">
      <div>
        <slot :open="open">
          <hawk-button type="text">
            <IconHawkPlus />  {{ $t('Add columns') }}
          </hawk-button>
        </slot>
      </div>
    </template>
    <template #content>
      <bi-query-builder-column-picker
        :stages="tables_with_fields" :show-stage-header="true"
        :is-dropdown="true" :has-functions="false" :stage-index="stageIndex" @selected="onColumnsSelected($event);updateDropdownPosition()" @expression="onExpressionClicked($event);updateDropdownPosition()"
      />
    </template>
  </hawk-menu>
</template>
