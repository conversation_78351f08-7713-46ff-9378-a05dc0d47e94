<script setup>
import { storeToRefs } from 'pinia';
import { computed, nextTick, onMounted } from 'vue';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { BI_CHART_BUILDER_TABS, BI_DEFAULT_PALETTE_COLORS, CHART_TO_CATEGORY_TYPE_MAP } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const emit = defineEmits(['goBack']);

const bi_store = useBiStore();
const { widget_builder_config, is_chart_builder_form_valid, chart_builder_columns_data_types } = storeToRefs(bi_store);
const { set_chart_builder_columns_data_types } = bi_store;

const { stack_by_field_columns, date_bin_units } = useBiChartBuilderHelpers();

const type_form$ = ref(null);
const form$ = ref(null);

const types = [
  ['table', 'Table', IconHawkTableTwo],
  ['pivot_table', 'Pivot table', IconHawkPivotTable],
  ['column_chart', 'Column chart', IconHawkHorizontalBarChartOne],
  ['horizontalBar_chart', 'Bar chart', IconHawkHorizontalBarChartOne],
  ['line_chart', 'Line chart', IconHawkLineChartUpOne],
  ['area_chart', 'Area chart', IconHawkAreaChart],
  ['mixed_chart', 'Mixed chart', IconHawkMixChart],
  ['pie_chart', 'Pie chart', IconHawkPieChartThree],
  ['donut_chart', 'Doughnut chart', IconHawkDoughnutChart],
  ['scatter_chart', 'Scatter chart', IconHawkScatterChart],
  ['gauge_chart', 'Gauge chart', IconHawkGaugeChart],
  ['progress_chart', 'Progress chart', IconHawkProgressChart],
  ['heatmap_chart', 'Heatmap chart', IconHawkHeatmapChart],
  ['pyramid_chart', 'Pyramid chart', IconHawkPyramidChart],
  ['funnel_chart', 'Funnel chart', IconHawkFunnelChart],
  ['pareto_chart', 'Pareto chart', IconHawkParetoChart],
  ['waterfall_chart', 'Waterfall chart', IconHawkWaterfallChart],
  ['number_chart', 'Number chart', IconHawkNumber],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
    icon: item[2],
  };
});

const state = reactive({
  form_data: {},
  active_item: 'layout',
});

const tabs = computed(() => {
  const current_chart_tabs = BI_CHART_BUILDER_TABS[widget_builder_config.value.chart.type] || [];
  return [
    ...(current_chart_tabs.includes('layout') ? [{ uid: 'layout', label: 'Layout' }] : []),
    ...(current_chart_tabs.includes('display') ? [{ uid: 'display', label: 'Display' }] : []),
    ...(current_chart_tabs.includes('axes') ? [{ uid: 'axes', label: 'Axes' }] : []),
    ...(current_chart_tabs.includes('advanced') ? [{ uid: 'advanced', label: 'Advanced' }] : []),
    ...(current_chart_tabs.includes('conditional_formatting') ? [{ uid: 'conditional_formatting', label: 'Conditional formatting' }] : []),
  ];
});

const rules = computed(() => {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type)) {
    return bi_store.widget_builder_config.chart.conditional_formatting;
  }
  return [];
});

// This is used to update the nested properties: 'layout_values'
function onSeriesConfigChange(fields, index) {
  if (state.form_data?.layout_values?.[index]) {
    let has_type_changed = false;
    Object.keys(fields).forEach((field_name) => {
      if (field_name === 'type' && state.form_data.layout_values[index].type && state.form_data.layout_values[index].type !== fields.type)
        has_type_changed = true;
      state.form_data.layout_values[index] = {
        ...state.form_data.layout_values[index],
        [field_name]: fields[field_name],
      };
    });
    if (has_type_changed) {
      type_form$.value.update({ type: 'mixed_chart' });
      onChartTypeChange('mixed_chart');
    }
  }
}

// This is used to update the color for 'gauge_chart', 'progress_chart'
function onPropertyChange(fields) {
  Object.keys(fields).forEach((field_name) => {
    state.form_data[field_name] = fields[field_name];
  });
}

// This is used to update the nested properties: 'color_ranges', 'reference_lines', 'markers'
function onConfigChange(fields, index, config_name) {
  if (state.form_data?.[config_name]?.[index]) {
    Object.keys(fields).forEach((field_name) => {
      state.form_data[config_name][index][field_name] = fields[field_name];
    });
  }
}

function onLayoutCategorySelected(event) {
  if (['column_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(widget_builder_config.value.chart.type) && chart_builder_columns_data_types.value[widget_builder_config.value.chart.layout_category] === 'date' && chart_builder_columns_data_types.value[event] !== 'date') {
    const updates = {};
    updates.layout_values = state.form_data.layout_values.filter(item => !item.forecast_config);
    form$.value.update(updates);
  }
}

async function onChartTypeChange(new_type) {
  const old_type = widget_builder_config.value.chart.type;
  if (['table', 'pivot_table'].includes(new_type)) {
    widget_builder_config.value.chart.conditional_formatting = [];
    widget_builder_config.value.chart = {
      ...widget_builder_config.value.chart,
      type: new_type,
    };
    state.active_item = 'layout';
    return;
  }

  const types_with_series = ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'];
  const is_moving_in_or_out_of_a_series = (types_with_series.includes(old_type) && !types_with_series.includes(new_type)) || (types_with_series.includes(new_type) && !types_with_series.includes(old_type));
  const updates = {};

  // Moving to a series chart from a number chart
  if (types_with_series.includes(new_type) && old_type === 'number_chart') {
    let type = new_type.replace('_chart', '');
    if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
      type = 'bar';
    updates.layout_values = state.form_data.layout_values.map((item, index) => {
      return {
        value: item.value,
        legend: item.value,
        type,
        color: BI_DEFAULT_PALETTE_COLORS[index % BI_DEFAULT_PALETTE_COLORS.length],
        y_axis: 'primary',
        line_style: 'solid',
        line_width: 2,
        line_shape: 'straight',
        prefix: state.form_data.prefix,
        suffix: state.form_data.suffix,
        stack: true,
      };
    });
  }
  // Moving to a number chart from a series chart
  else if (new_type === 'number_chart' && types_with_series.includes(old_type)) {
    updates.layout_values = state.form_data.layout_values.filter(item => !item.forecast_config).map(item => ({ value: item.value }));
  }
  // Moving into a non-series chart from a number chart
  else if (old_type === 'number_chart' && !types_with_series.includes(new_type)) {
    updates.layout_values = state.form_data.layout_values[0].value;
  }
  // Moving out of a non-series chart into a number chart
  else if (new_type === 'number_chart' && !types_with_series.includes(old_type)) {
    updates.layout_values = [{
      value: state.form_data.layout_values,
    }];
  }
  // Moving into a series or out of a series
  else if (is_moving_in_or_out_of_a_series) {
    if (types_with_series.includes(new_type)) {
      let type = new_type.replace('_chart', '');
      if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
        type = 'bar';
      updates.layout_values = [{
        value: state.form_data.layout_values,
        legend: state.form_data.layout_values,
        type,
        color: BI_DEFAULT_PALETTE_COLORS[0],
        y_axis: 'primary',
        line_style: 'solid',
        line_width: 2,
        line_shape: 'straight',
        prefix: '',
        suffix: '',
        stack: true,
      }];
    }
    else {
      const first_series_without_forecast = state.form_data.layout_values.find(item => !item.forecast_config);
      updates.layout_values = first_series_without_forecast.value;
    }
  }

  // Moving into a heatmap
  if (new_type === 'heatmap_chart' && !stack_by_field_columns.value.includes(state.form_data.stack_by)) {
    updates.stack_by = null;
  }

  // Checking if the category field is still valid
  // ?. because a few charts like gauge and progress don't have category fields
  if (!CHART_TO_CATEGORY_TYPE_MAP[new_type]?.includes?.(chart_builder_columns_data_types.value[state.form_data.layout_category]))
    updates.layout_category = null;
  form$.value.update(updates);

  widget_builder_config.value.chart = {
    ...widget_builder_config.value.chart,
    type: new_type,
  };
  state.active_item = 'layout';

  // Switching to column, horizontalBar, line and area charts from themselves or mixed/scatter
  if (!is_moving_in_or_out_of_a_series && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart'].includes(new_type)) {
    await nextTick();
    form$.value.update({
      layout_values: state.form_data.layout_values.filter((item) => {
        const type = new_type.replace('_chart', '');
        if (type === 'horizontalBar')
          return !item.forecast_config;
        return true;
      }).map((item) => {
        let type = new_type.replace('_chart', '');
        if (type === 'column' || type === 'horizontalBar')
          type = 'bar';
        return { ...item, type };
      }),
    });
  }

  // Set the default values for non-vueform fields
  if (new_type === 'pareto_chart') {
    await nextTick();
    form$.value.update({
      bar_color: BI_DEFAULT_PALETTE_COLORS[0],
      cumulative_line_color: BI_DEFAULT_PALETTE_COLORS[2],
      show_eighty_percent_line: false,
      eighty_percent_line_color: BI_DEFAULT_PALETTE_COLORS[1],
      eighty_percent_line_style: 'dashed',
    });
  }
  else if (new_type === 'waterfall_chart') {
    await nextTick();
    form$.value.update({
      show_sum: false,
      positive_color: BI_DEFAULT_PALETTE_COLORS[1],
      negative_color: BI_DEFAULT_PALETTE_COLORS[2],
    });
  }
  else if (new_type === 'number_chart') {
    await nextTick();
    form$.value.update({
      number_widget_color: '#FFFFFF',
    });
  }

  await validateForm();
}

async function validateForm() {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type))
    return;
  setTimeout(async () => {
    await form$.value?.validate?.();
    if (form$.value?.invalid === false)
      is_chart_builder_form_valid.value = true;
    else
      is_chart_builder_form_valid.value = false;
  }, 0);
}

function onUpdateRules(rules) {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type)) {
    bi_store.widget_builder_config.chart.conditional_formatting = rules;
    bi_store.table_widget_config_change_detector = true;
  }
}

watch(() => state.form_data, async (new_config) => {
  if (['table', 'pivot_table'].includes(widget_builder_config.value.chart.type))
    return;
  await validateForm();
  // Exclude 'type' from the update to prevent race condition with onChartTypeChange
  const { type, ...config_without_type } = new_config;
  widget_builder_config.value.chart = {
    ...widget_builder_config.value.chart,
    ...config_without_type,
  };
}, { deep: true });

watch(date_bin_units, (new_val, old_val) => {
  if (new_val !== old_val) {
    const updates = {};
    updates.layout_values = state.form_data.layout_values.map(item => item.forecast_config ? ({ ...item, forecast_config: { ...item.forecast_config, bin_units: new_val } }) : item);
    form$.value.update(updates);
  }
});

onMounted(async () => {
  set_chart_builder_columns_data_types();
  if (!widget_builder_config.value.chart.type) {
    widget_builder_config.value.chart = {
      type: 'table',
    };
  }
  else {
    state.form_data = {
      ...widget_builder_config.value.chart,
    };
    type_form$.value.load({ type: widget_builder_config.value.chart.type });

    if (!['table', 'pivot_table'].includes(widget_builder_config.value.chart.type)) {
      const columns = Object.keys(chart_builder_columns_data_types.value);

      if (!columns.includes(state.form_data.layout_category)) {
        state.form_data.layout_category = null;
      }

      if (Array.isArray(state.form_data.layout_values)) {
        state.form_data.layout_values = state.form_data.layout_values.filter(item => columns.includes(item.value) || columns.includes(item.forecast_config?.forecast_for_series));

        if (!state.form_data.layout_category || chart_builder_columns_data_types.value[state.form_data.layout_category] !== 'date') {
          state.form_data.layout_values = state.form_data.layout_values.filter(item => !item.forecast_config);
        }
      }
      else if (!columns.includes(state.form_data.layout_values)) {
        state.form_data.layout_values = null;
      }

      if (!stack_by_field_columns.value.includes(state.form_data.stack_by) && state.form_data.stack_by !== true) {
        state.form_data.stack_by = 'none';
      }
    }

    await validateForm();
  }
});
</script>

<template>
  <div>
    <div
      class="flex items-center gap-2 cursor-pointer hover:underline text-sm font-semibold text-gray-700"
      @click="emit('goBack')"
    >
      <IconHawkArrowLeft />
      Back to data builder
    </div>

    <Vueform
      ref="type_form$"
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      class="mt-6"
    >
      <SelectElement
        name="type"
        default="table"
        :label="$t('Chart type')"
        :items="types"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
        :search="true"
        @select="onChartTypeChange"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <component :is="option.icon" class="text-gray-500" :class="{ '-rotate-90': option.value === 'column_chart' }" />
            {{ option.label }}
          </div>
        </template>
        <template #single-label="{ value }">
          <div class="w-full flex items-center gap-2 px-2">
            <component :is="value.icon" class="text-gray-500" :class="{ '-rotate-90': value.value === 'column_chart' }" />
            {{ value.label }}
          </div>
        </template>
      </SelectElement>
    </Vueform>
    <HawkTabs v-if="widget_builder_config.chart.type" :tabs="tabs" :active_item="state.active_item" class="col-span-12 mt-6" @tab-click="state.active_item = $event.uid" />
    <Vueform
      :key="widget_builder_config.chart.type"
      ref="form$"
      v-model="state.form_data"
      sync
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      :display-errors="false"
      :display-messages="false"
      :messages="{ required: $t('This field is required') }"
      class="mt-3"
    >
      <div v-show="state.active_item === 'layout'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartLayoutTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
          :form-instance="form$"
          @series-config-change="onSeriesConfigChange"
          @property-change="onPropertyChange"
          @layout-category-selected="onLayoutCategorySelected"
        />
      </div>
      <div v-show="state.active_item === 'display'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartDisplayTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
          :form-instance="form$"
          @config-change="onConfigChange"
        />
      </div>
      <div v-show="state.active_item === 'axes'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAxesTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
        />
      </div>
      <div v-show="state.active_item === 'advanced'" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAdvancedTab
          :chart-type="widget_builder_config.chart.type"
          :chart-config="widget_builder_config.chart"
          :form-instance="form$"
          @config-change="onConfigChange"
        />
      </div>
      <div v-if="state.active_item === 'conditional_formatting'" class="col-span-12 flex flex-col gap-y-5">
        <BiConditionalFormattingTab
          :chart-type="widget_builder_config.chart.type"
          :rules="rules"
          @update-rules="onUpdateRules"
        />
      </div>
    </Vueform>
  </div>
</template>
