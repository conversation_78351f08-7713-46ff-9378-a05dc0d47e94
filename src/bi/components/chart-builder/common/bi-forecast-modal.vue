<script setup>
import { cloneDeep } from 'lodash-es';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';

const props = defineProps({
  mode: {
    type: String,
    default: 'create',
    validator: value => ['create', 'edit'].includes(value),
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
  chartConfig: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close', 'save']);

const $toast = inject('$toast');

const { all_columns, date_bin_label, date_bin_units } = useBiChartBuilderHelpers();

const form$ = ref(null);

const state = reactive({
  is_loading: false,
  form_data: {},
});

const series_options = computed(() => {
  return props.chartConfig.layout_values.filter(item => !item.forecast_config).map(item => item.value);
});

function onSave() {
  if (props.mode === 'create' && (all_columns.value.find(col => col === state.form_data.forecast_series_name) || props.chartConfig.layout_values.find(item => item.forecast_config?.forecast_series_name === state.form_data.forecast_series_name))) {
    $toast({
      title: 'Series name already exists',
      text: 'Please choose a different name',
      type: 'error',
    });
    return;
  }
  emit('save', state.form_data);
}

onMounted(() => {
  if (props.mode === 'edit') {
    state.form_data = cloneDeep(props.initialData);
  }
  else {
    state.form_data.forecast_series_name = `[Forecast] ${series_options.value[0]}`;
  }
});
</script>

<template>
  <HawkModalContainer id="bi-forecast-modal-container">
    <Vueform
      ref="form$"
      v-model="state.form_data"
      sync
      size="sm"
      class="max-w-[600px]"
      :columns="{
        default: { container: 12, label: 4, wrapper: 12 },
        sm: { container: 12, label: 4, wrapper: 12 },
        md: { container: 12, label: 4, wrapper: 12 },
      }"
      :display-errors="false"
      :display-messages="false"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ props.mode === 'create' ? 'New forecast series' : 'Edit forecast series' }}
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent class="flex flex-col gap-6">
          <SelectElement
            name="forecast_for_series"
            label="Series"
            :items="series_options"
            :default="series_options[0]"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            :rules="['required']"
            :disabled="props.mode === 'edit'"
            append-to="#bi-forecast-modal-container"
            @select="state.form_data.forecast_series_name = `[Forecast] ${$event}`"
          />
          <TextElement
            name="forecast_series_name"
            label="Name"
            :rules="['required']"
            :disabled="props.mode === 'edit'"
          />
          <RadiogroupElement
            name="starting_from"
            label="Starting from"
            :items="{
              lastDataPoint: 'Last entry',
              today: 'Today',
            }"
            default="lastDataPoint"
            :rules="['required']"
          />
          <TextElement
            name="forecast_for"
            label="For"
            :rules="['required']"
            input-type="number"
            :attrs="{ min: 1 }"
            :default="6"
          >
            <template #addon-after>
              {{ date_bin_label }}
            </template>
          </TextElement>
          <ObjectElement
            label="At the rate of"
            name="rate"
          >
            <div class="col-span-12 flex items-center gap-1">
              <SelectElement
                name="aggregation"
                :items="{
                  average: 'Average',
                  median: 'Median',
                  sum: 'Sum',
                  min: 'Minimum',
                  max: 'Maximum',
                }"
                default="average"
                :native="false"
                :can-clear="false"
                :can-deselect="false"
                :rules="['required']"
                class="w-1/3"
                append-to="#bi-forecast-modal-container"
              />
              over
              <SelectElement
                name="rate_over"
                :items="{
                  all: 'All values',
                  last: 'Last',
                  first: 'First',
                }"
                default="all"
                :native="false"
                :can-clear="false"
                :can-deselect="false"
                :rules="['required']"
                class="w-1/3"
                append-to="#bi-forecast-modal-container"
              />
              <template v-if="state.form_data.rate.rate_over !== 'all'">
                <TextElement
                  name="rate_over_number"
                  :rules="['required']"
                  input-type="number"
                  :attrs="{ min: 1 }"
                  :default="2"
                  class="w-1/3"
                >
                  <template #addon-after>
                    {{ date_bin_label }}
                  </template>
                </TextElement>
              </template>
            </div>
          </ObjectElement>
          <ToggleElement
            name="consider_zeros"
            label="Consider zeros"
          />
          <HiddenElement
            name="bin_units"
            :default="date_bin_units"
          />
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="emit('close')"
              >
                {{ $t('Cancel') }}
              </ButtonElement>
              <ButtonElement
                name="rename"
                :loading="state.is_loading"
                @click="onSave"
              >
                {{ $t('Save') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
